name: Web Tests

on:
  pull_request:
    branches:
      - main
    paths:
      - web/**

concurrency:
  group: web-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  test:
    name: Web Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./web

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: web/**

      - name: Install pnpm
        if: steps.changed-files.outputs.any_changed == 'true'
        uses: pnpm/action-setup@v4
        with:
          version: 10
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        if: steps.changed-files.outputs.any_changed == 'true'
        with:
          node-version: 22
          cache: pnpm
          cache-dependency-path: ./web/package.json

      - name: Install dependencies
        if: steps.changed-files.outputs.any_changed == 'true'
        run: pnpm install --frozen-lockfile

      - name: Run tests
        if: steps.changed-files.outputs.any_changed == 'true'
        run: pnpm test
