# Dify Java 环境配置示例
# 复制此文件为 .env 并修改相应配置

# ===========================================
# 基础配置
# ===========================================

# 环境类型 (dev/test/prod)
ENVIRONMENT=dev

# 日志级别
LOG_LEVEL=INFO

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=dify
DB_USER=postgres
DB_PASSWORD=difyai123456

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=difyai123456
REDIS_DB=0

# ===========================================
# 模型提供商配置
# ===========================================

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com

# Azure OpenAI配置
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Google配置
GOOGLE_API_KEY=your_google_api_key_here

# ===========================================
# 向量数据库配置
# ===========================================

# Weaviate配置
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
WEAVIATE_SCHEME=http
WEAVIATE_API_KEY=

# Qdrant配置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=

# Pinecone配置
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here

# ===========================================
# 文件存储配置
# ===========================================

# 本地存储
LOCAL_STORAGE_PATH=/tmp/dify/storage

# S3配置
S3_ACCESS_KEY_ID=your_s3_access_key_here
S3_SECRET_ACCESS_KEY=your_s3_secret_key_here
S3_BUCKET_NAME=your_s3_bucket_here
S3_REGION=us-east-1

# ===========================================
# 安全配置
# ===========================================

# JWT密钥
JWT_SECRET=dify-java-secret-key-2024-change-this-in-production

# 加密密钥
ENCRYPTION_KEY=your-32-character-encryption-key-here

# ===========================================
# 外部服务配置
# ===========================================

# 邮件服务配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_FROM=<EMAIL>

# 短信服务配置
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# ===========================================
# 监控和分析配置
# ===========================================

# Sentry配置
SENTRY_DSN=your_sentry_dsn_here

# Google Analytics配置
GA_TRACKING_ID=your_ga_tracking_id_here

# ===========================================
# 功能开关
# ===========================================

# 启用注册
ENABLE_REGISTRATION=true

# 启用邮箱验证
ENABLE_EMAIL_VERIFICATION=false

# 启用多租户
ENABLE_MULTI_TENANT=true

# 启用API限流
ENABLE_RATE_LIMITING=true

# 启用缓存
ENABLE_CACHE=true

# ===========================================
# 性能配置
# ===========================================

# 数据库连接池大小
DB_POOL_SIZE=20

# Redis连接池大小
REDIS_POOL_SIZE=20

# HTTP客户端超时(秒)
HTTP_TIMEOUT=60

# 文件上传大小限制(MB)
MAX_FILE_SIZE=100

# ===========================================
# 开发配置
# ===========================================

# 启用调试模式
DEBUG_MODE=false

# 启用SQL日志
ENABLE_SQL_LOG=false

# 启用性能监控
ENABLE_PERFORMANCE_MONITORING=false

# Mock外部服务
MOCK_EXTERNAL_SERVICES=false
