# Dify Java

Dify的Java实现版本，提供完整的AI应用开发平台功能。

## 项目概述

Dify Java是基于Spring Boot框架开发的企业级AI应用平台，提供以下核心功能：

- 🤖 **多模型支持**: 支持OpenAI、Anthropic等多种LLM提供商
- 🔧 **工作流引擎**: 可视化工作流设计和执行
- 📚 **RAG服务**: 检索增强生成，支持知识库管理
- 🎯 **应用管理**: 完整的AI应用生命周期管理
- 🔌 **API服务**: RESTful API和流式API支持
- 🎨 **管理控制台**: Web管理界面

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐
│   Console API   │    │   Service API   │
│     (5001)      │    │     (5002)      │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │  Nginx Gateway  │
         │      (80)       │
         └─────────────────┘
                     │
    ┌────────────────┼────────────────┐
    │                │                │
┌─────────┐    ┌─────────┐    ┌─────────┐
│PostgreSQL│    │  Redis  │    │Weaviate │
│  (5432) │    │ (6379)  │    │ (8080)  │
└─────────┘    └─────────┘    └─────────┘
```

## 模块结构

### 核心模块

- **dify-common**: 公共工具类和基础组件
- **dify-domain**: 领域模型和实体类
- **dify-console-api**: 控制台管理API服务
- **dify-service-api**: 对外服务API
- **dify-workflow-engine**: 工作流引擎
- **dify-model-runtime**: 模型运行时
- **dify-rag-service**: RAG检索增强生成服务
- **dify-gateway**: API网关
- **dify-web**: 前端Web应用

### 技术栈

- **后端框架**: Spring Boot 3.2.0, Spring Cloud 2023.0.0
- **数据库**: PostgreSQL, Redis
- **向量数据库**: Weaviate, Qdrant
- **ORM**: MyBatis Plus
- **前端**: React + TypeScript
- **构建工具**: Maven
- **容器化**: Docker

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.8+
- Docker & Docker Compose
- Node.js 18+ (前端开发)

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd dify_java
```

2. 启动基础服务
```bash
docker-compose up -d postgres redis
```

3. 编译项目
```bash
mvn clean compile
```

4. 启动服务
```bash
# 启动网关
cd dify-gateway && mvn spring-boot:run

# 启动控制台API
cd dify-console-api && mvn spring-boot:run

# 启动服务API
cd dify-service-api && mvn spring-boot:run
```

5. 启动前端
```bash
cd dify-web
npm install
npm start
```

### Docker部署

```bash
docker-compose up -d
```

## 开发指南

### 代码规范

- 使用Lombok减少样板代码
- 使用MapStruct进行对象映射
- 遵循RESTful API设计原则
- 使用统一的异常处理和响应格式

### 数据库设计

参考原版Dify的数据库设计，使用PostgreSQL作为主数据库，Redis作为缓存和消息队列。

### API设计

- Console API: `/console/api/**` - 管理后台接口
- Service API: `/v1/**` - 对外服务接口
- Web API: `/api/**` - Web应用接口

## 功能特性

- ✅ 多模型支持 (OpenAI, Anthropic, 本地模型等)
- ✅ 可视化工作流编排
- ✅ RAG知识库管理
- ✅ 应用发布和管理
- ✅ 用户权限控制
- ✅ 插件系统
- ✅ 多语言支持

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

本项目采用Apache 2.0许可证。
