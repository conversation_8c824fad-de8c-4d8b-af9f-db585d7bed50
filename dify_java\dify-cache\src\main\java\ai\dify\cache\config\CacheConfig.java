package ai.dify.cache.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 缓存配置
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Value("${dify.cache.caffeine.maximum-size:10000}")
    private long caffeineMaximumSize;

    @Value("${dify.cache.caffeine.expire-after-write:30m}")
    private Duration caffeineExpireAfterWrite;

    @Value("${dify.cache.redis.default-ttl:1h}")
    private Duration redisDefaultTtl;

    /**
     * Caffeine 本地缓存管理器
     */
    @Bean
    @Primary
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(caffeineMaximumSize)
                .expireAfterWrite(caffeineExpireAfterWrite)
                .recordStats() // 启用统计
                .build());
        
        return cacheManager;
    }

    /**
     * Redis 分布式缓存管理器
     */
    @Bean("redisCacheManager")
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(redisDefaultTtl)
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();

        // 特定缓存配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 模型响应缓存 - 较长过期时间
        cacheConfigurations.put("model_response", defaultConfig.entryTtl(Duration.ofHours(6)));
        
        // 嵌入向量缓存 - 很长过期时间
        cacheConfigurations.put("embedding", defaultConfig.entryTtl(Duration.ofDays(7)));
        
        // 用户会话缓存 - 较短过期时间
        cacheConfigurations.put("user_session", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 应用配置缓存 - 中等过期时间
        cacheConfigurations.put("app_config", defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // 数据集信息缓存 - 中等过期时间
        cacheConfigurations.put("dataset_info", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 工作流定义缓存 - 较长过期时间
        cacheConfigurations.put("workflow_definition", defaultConfig.entryTtl(Duration.ofHours(4)));
        
        // 工具结果缓存 - 较短过期时间
        cacheConfigurations.put("tool_result", defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        // 文件内容缓存 - 较长过期时间
        cacheConfigurations.put("file_content", defaultConfig.entryTtl(Duration.ofHours(12)));
        
        // 搜索结果缓存 - 较短过期时间
        cacheConfigurations.put("search_result", defaultConfig.entryTtl(Duration.ofMinutes(10)));
        
        // 限流缓存 - 很短过期时间
        cacheConfigurations.put("rate_limit", defaultConfig.entryTtl(Duration.ofMinutes(1)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * Caffeine 缓存构建器
     */
    @Bean
    public Caffeine<Object, Object> caffeineConfig() {
        return Caffeine.newBuilder()
                .maximumSize(caffeineMaximumSize)
                .expireAfterWrite(caffeineExpireAfterWrite.toMinutes(), TimeUnit.MINUTES)
                .recordStats();
    }
}
