package ai.dify.cache.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 缓存键
 */
@Data
public class CacheKey implements Serializable {

    private String namespace;
    private String key;
    private String version;

    public CacheKey() {}

    public CacheKey(String namespace, String key) {
        this.namespace = namespace;
        this.key = key;
        this.version = "1.0";
    }

    public CacheKey(String namespace, String key, String version) {
        this.namespace = namespace;
        this.key = key;
        this.version = version;
    }

    /**
     * 构建完整的缓存键
     */
    public String buildKey() {
        if (version != null && !version.isEmpty()) {
            return String.format("%s:%s:v%s", namespace, key, version);
        }
        return String.format("%s:%s", namespace, key);
    }

    /**
     * 创建模型响应缓存键
     */
    public static CacheKey modelResponse(String provider, String model, String inputHash) {
        String key = String.format("%s:%s:%s", provider, model, inputHash);
        return new CacheKey("model_response", key);
    }

    /**
     * 创建嵌入缓存键
     */
    public static CacheKey embedding(String provider, String model, String textHash) {
        String key = String.format("%s:%s:%s", provider, model, textHash);
        return new CacheKey("embedding", key);
    }

    /**
     * 创建用户会话缓存键
     */
    public static CacheKey userSession(String userId, String sessionId) {
        String key = String.format("%s:%s", userId, sessionId);
        return new CacheKey("user_session", key);
    }

    /**
     * 创建应用配置缓存键
     */
    public static CacheKey appConfig(String appId) {
        return new CacheKey("app_config", appId);
    }

    /**
     * 创建数据集信息缓存键
     */
    public static CacheKey datasetInfo(String datasetId) {
        return new CacheKey("dataset_info", datasetId);
    }

    /**
     * 创建工作流定义缓存键
     */
    public static CacheKey workflowDefinition(String workflowId, String version) {
        return new CacheKey("workflow_definition", workflowId, version);
    }

    /**
     * 创建工具结果缓存键
     */
    public static CacheKey toolResult(String toolName, String parametersHash) {
        String key = String.format("%s:%s", toolName, parametersHash);
        return new CacheKey("tool_result", key);
    }

    /**
     * 创建文件内容缓存键
     */
    public static CacheKey fileContent(String fileId, String version) {
        return new CacheKey("file_content", fileId, version);
    }

    /**
     * 创建搜索结果缓存键
     */
    public static CacheKey searchResult(String query, String filters) {
        String key = String.format("%s:%s", query, filters != null ? filters : "");
        return new CacheKey("search_result", key);
    }

    /**
     * 创建限流缓存键
     */
    public static CacheKey rateLimit(String identifier, String action) {
        String key = String.format("%s:%s", identifier, action);
        return new CacheKey("rate_limit", key);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CacheKey cacheKey = (CacheKey) o;
        return Objects.equals(namespace, cacheKey.namespace) &&
               Objects.equals(key, cacheKey.key) &&
               Objects.equals(version, cacheKey.version);
    }

    @Override
    public int hashCode() {
        return Objects.hash(namespace, key, version);
    }

    @Override
    public String toString() {
        return buildKey();
    }
}
