package ai.dify.cache.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 缓存统计信息
 */
@Data
public class CacheStats {

    /**
     * 缓存名称
     */
    private String cacheName;

    /**
     * 命中次数
     */
    private long hitCount;

    /**
     * 未命中次数
     */
    private long missCount;

    /**
     * 命中率
     */
    private double hitRate;

    /**
     * 驱逐次数
     */
    private long evictionCount;

    /**
     * 键数量
     */
    private long keyCount;

    /**
     * 统计时间
     */
    private LocalDateTime timestamp;

    public CacheStats() {
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 计算命中率
     */
    public double calculateHitRate() {
        long totalRequests = hitCount + missCount;
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) hitCount / totalRequests;
    }

    /**
     * 获取总请求数
     */
    public long getTotalRequests() {
        return hitCount + missCount;
    }

    /**
     * 获取未命中率
     */
    public double getMissRate() {
        return 1.0 - hitRate;
    }
}
