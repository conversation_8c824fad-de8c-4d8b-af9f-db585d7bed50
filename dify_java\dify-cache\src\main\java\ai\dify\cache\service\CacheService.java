package ai.dify.cache.service;

import ai.dify.cache.model.CacheKey;
import ai.dify.cache.model.CacheStats;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheService {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 存储缓存
     */
    public void put(String cacheName, String key, Object value) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.put(key, value);
                log.debug("缓存已存储: cache={}, key={}", cacheName, key);
            }
        } catch (Exception e) {
            log.error("存储缓存失败: cache={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 存储缓存（带过期时间）
     */
    public void put(String cacheName, String key, Object value, Duration ttl) {
        try {
            // 使用Redis存储带TTL的缓存
            String fullKey = buildFullKey(cacheName, key);
            redisTemplate.opsForValue().set(fullKey, value, ttl);
            log.debug("缓存已存储（TTL）: cache={}, key={}, ttl={}", cacheName, key, ttl);
        } catch (Exception e) {
            log.error("存储缓存失败（TTL）: cache={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 获取缓存
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String cacheName, String key, Class<T> type) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    Object value = wrapper.get();
                    if (type.isInstance(value)) {
                        log.debug("缓存命中: cache={}, key={}", cacheName, key);
                        return (T) value;
                    }
                }
            }
            
            // 尝试从Redis获取
            String fullKey = buildFullKey(cacheName, key);
            Object value = redisTemplate.opsForValue().get(fullKey);
            if (value != null && type.isInstance(value)) {
                log.debug("Redis缓存命中: cache={}, key={}", cacheName, key);
                return (T) value;
            }
            
            log.debug("缓存未命中: cache={}, key={}", cacheName, key);
            return null;
            
        } catch (Exception e) {
            log.error("获取缓存失败: cache={}, key={}", cacheName, key, e);
            return null;
        }
    }

    /**
     * 获取缓存（如果不存在则计算）
     */
    public <T> T get(String cacheName, String key, Class<T> type, 
                     java.util.function.Supplier<T> valueLoader) {
        T value = get(cacheName, key, type);
        if (value == null) {
            value = valueLoader.get();
            if (value != null) {
                put(cacheName, key, value);
            }
        }
        return value;
    }

    /**
     * 获取缓存（如果不存在则计算，带TTL）
     */
    public <T> T get(String cacheName, String key, Class<T> type, 
                     java.util.function.Supplier<T> valueLoader, Duration ttl) {
        T value = get(cacheName, key, type);
        if (value == null) {
            value = valueLoader.get();
            if (value != null) {
                put(cacheName, key, value, ttl);
            }
        }
        return value;
    }

    /**
     * 删除缓存
     */
    public void evict(String cacheName, String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.evict(key);
            }
            
            // 同时删除Redis中的缓存
            String fullKey = buildFullKey(cacheName, key);
            redisTemplate.delete(fullKey);
            
            log.debug("缓存已删除: cache={}, key={}", cacheName, key);
        } catch (Exception e) {
            log.error("删除缓存失败: cache={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 清空缓存
     */
    public void clear(String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
            
            // 清空Redis中的相关缓存
            String pattern = buildFullKey(cacheName, "*");
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
            
            log.debug("缓存已清空: cache={}", cacheName);
        } catch (Exception e) {
            log.error("清空缓存失败: cache={}", cacheName, e);
        }
    }

    /**
     * 检查缓存是否存在
     */
    public boolean exists(String cacheName, String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    return true;
                }
            }
            
            // 检查Redis
            String fullKey = buildFullKey(cacheName, key);
            return Boolean.TRUE.equals(redisTemplate.hasKey(fullKey));
            
        } catch (Exception e) {
            log.error("检查缓存存在性失败: cache={}, key={}", cacheName, key, e);
            return false;
        }
    }

    /**
     * 设置缓存过期时间
     */
    public void expire(String cacheName, String key, Duration ttl) {
        try {
            String fullKey = buildFullKey(cacheName, key);
            redisTemplate.expire(fullKey, ttl);
            log.debug("缓存过期时间已设置: cache={}, key={}, ttl={}", cacheName, key, ttl);
        } catch (Exception e) {
            log.error("设置缓存过期时间失败: cache={}, key={}", cacheName, key, e);
        }
    }

    /**
     * 获取缓存剩余过期时间
     */
    public Duration getTtl(String cacheName, String key) {
        try {
            String fullKey = buildFullKey(cacheName, key);
            Long ttl = redisTemplate.getExpire(fullKey, TimeUnit.SECONDS);
            if (ttl != null && ttl > 0) {
                return Duration.ofSeconds(ttl);
            }
            return null;
        } catch (Exception e) {
            log.error("获取缓存过期时间失败: cache={}, key={}", cacheName, key, e);
            return null;
        }
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats(String cacheName) {
        CacheStats stats = new CacheStats();
        stats.setCacheName(cacheName);
        
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                // 获取本地缓存统计
                if (cache.getNativeCache() instanceof com.github.benmanes.caffeine.cache.Cache) {
                    @SuppressWarnings("unchecked")
                    com.github.benmanes.caffeine.cache.Cache<Object, Object> caffeineCache = 
                        (com.github.benmanes.caffeine.cache.Cache<Object, Object>) cache.getNativeCache();
                    
                    com.github.benmanes.caffeine.cache.stats.CacheStats caffeineStats = 
                        caffeineCache.stats();
                    
                    stats.setHitCount(caffeineStats.hitCount());
                    stats.setMissCount(caffeineStats.missCount());
                    stats.setHitRate(caffeineStats.hitRate());
                    stats.setEvictionCount(caffeineStats.evictionCount());
                }
            }
            
            // 获取Redis缓存键数量
            String pattern = buildFullKey(cacheName, "*");
            Set<String> keys = redisTemplate.keys(pattern);
            stats.setKeyCount(keys != null ? keys.size() : 0);
            
        } catch (Exception e) {
            log.error("获取缓存统计失败: cache={}", cacheName, e);
        }
        
        return stats;
    }

    /**
     * 获取所有缓存名称
     */
    public Collection<String> getCacheNames() {
        return cacheManager.getCacheNames();
    }

    /**
     * 批量获取缓存
     */
    public <T> Map<String, T> multiGet(String cacheName, Collection<String> keys, Class<T> type) {
        Map<String, T> result = new HashMap<>();
        
        for (String key : keys) {
            T value = get(cacheName, key, type);
            if (value != null) {
                result.put(key, value);
            }
        }
        
        return result;
    }

    /**
     * 批量存储缓存
     */
    public void multiPut(String cacheName, Map<String, Object> keyValues) {
        for (Map.Entry<String, Object> entry : keyValues.entrySet()) {
            put(cacheName, entry.getKey(), entry.getValue());
        }
    }

    /**
     * 批量删除缓存
     */
    public void multiEvict(String cacheName, Collection<String> keys) {
        for (String key : keys) {
            evict(cacheName, key);
        }
    }

    /**
     * 构建完整的缓存键
     */
    private String buildFullKey(String cacheName, String key) {
        return "dify:cache:" + cacheName + ":" + key;
    }

    /**
     * 缓存键常量
     */
    public static class CacheNames {
        public static final String MODEL_RESPONSE = "model_response";
        public static final String EMBEDDING = "embedding";
        public static final String USER_SESSION = "user_session";
        public static final String APP_CONFIG = "app_config";
        public static final String DATASET_INFO = "dataset_info";
        public static final String WORKFLOW_DEFINITION = "workflow_definition";
        public static final String TOOL_RESULT = "tool_result";
        public static final String FILE_CONTENT = "file_content";
        public static final String SEARCH_RESULT = "search_result";
        public static final String RATE_LIMIT = "rate_limit";
    }
}
