package ai.dify.common.exception;

import ai.dify.common.result.ResultCode;
import lombok.Getter;

/**
 * Dify业务异常
 */
@Getter
public class DifyException extends RuntimeException {

    private final Integer code;
    private final String message;

    public DifyException(String message) {
        super(message);
        this.code = ResultCode.ERROR.getCode();
        this.message = message;
    }

    public DifyException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public DifyException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    public DifyException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
        this.message = message;
    }

    public DifyException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResultCode.ERROR.getCode();
        this.message = message;
    }

    public DifyException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public DifyException(ResultCode resultCode, Throwable cause) {
        super(resultCode.getMessage(), cause);
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
}
