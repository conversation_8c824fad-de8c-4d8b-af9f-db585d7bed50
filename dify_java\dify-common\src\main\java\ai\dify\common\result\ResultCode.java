package ai.dify.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 业务状态码
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已禁用"),
    PASSWORD_ERROR(1003, "密码错误"),
    TOKEN_INVALID(1004, "Token无效"),
    TOKEN_EXPIRED(1005, "Token已过期"),

    APP_NOT_FOUND(2001, "应用不存在"),
    APP_DISABLED(2002, "应用已禁用"),
    APP_CONFIG_ERROR(2003, "应用配置错误"),

    DATASET_NOT_FOUND(3001, "数据集不存在"),
    DATASET_PROCESSING(3002, "数据集处理中"),
    DOCUMENT_NOT_FOUND(3003, "文档不存在"),
    DOCUMENT_PROCESSING(3004, "文档处理中"),

    WORKFLOW_NOT_FOUND(4001, "工作流不存在"),
    WORKFLOW_RUNNING(4002, "工作流运行中"),
    WORKFLOW_CONFIG_ERROR(4003, "工作流配置错误"),
    NODE_EXECUTION_ERROR(4004, "节点执行错误"),

    MODEL_NOT_FOUND(5001, "模型不存在"),
    MODEL_UNAVAILABLE(5002, "模型不可用"),
    MODEL_QUOTA_EXCEEDED(5003, "模型配额已用完"),
    MODEL_REQUEST_ERROR(5004, "模型请求错误"),

    FILE_UPLOAD_ERROR(6001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(6002, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(6003, "文件大小超限"),
    FILE_NOT_FOUND(6004, "文件不存在"),

    VECTOR_STORE_ERROR(7001, "向量存储错误"),
    EMBEDDING_ERROR(7002, "向量化失败"),
    RETRIEVAL_ERROR(7003, "检索失败");

    private final Integer code;
    private final String message;
}
