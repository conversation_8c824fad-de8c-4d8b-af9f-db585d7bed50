# 多阶段构建
FROM maven:3.9-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制pom文件
COPY pom.xml .
COPY dify-common/pom.xml dify-common/
COPY dify-domain/pom.xml dify-domain/
COPY dify-console-api/pom.xml dify-console-api/

# 下载依赖
RUN mvn dependency:go-offline -B

# 复制源代码
COPY dify-common/src dify-common/src
COPY dify-domain/src dify-domain/src
COPY dify-console-api/src dify-console-api/src

# 构建应用
RUN mvn clean package -DskipTests -pl dify-console-api -am

# 运行阶段
FROM openjdk:17-jre-slim

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r dify && useradd -r -g dify dify

# 设置工作目录
WORKDIR /app

# 复制jar文件
COPY --from=builder /app/dify-console-api/target/dify-console-api-*.jar app.jar

# 创建日志目录
RUN mkdir -p /var/log/dify && chown -R dify:dify /var/log/dify
RUN mkdir -p /tmp/dify && chown -R dify:dify /tmp/dify

# 切换到应用用户
USER dify

# 暴露端口
EXPOSE 5001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5001/console/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "-Xmx1g", "-Xms512m", \
    "-Djava.security.egd=file:/dev/./urandom", \
    "-Dspring.profiles.active=docker", \
    "app.jar"]
