package ai.dify.console;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Console API应用启动类
 */
@SpringBootApplication
@ComponentScan(basePackages = {"ai.dify.console", "ai.dify.common", "ai.dify.domain"})
@MapperScan("ai.dify.domain.repository")
public class ConsoleApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ConsoleApiApplication.class, args);
    }
}
