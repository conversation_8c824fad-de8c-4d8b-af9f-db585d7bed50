package ai.dify.console.controller;

import ai.dify.common.result.Result;
import ai.dify.console.dto.AppCreateRequest;
import ai.dify.console.dto.AppResponse;
import ai.dify.console.dto.AppUpdateRequest;
import ai.dify.console.service.AppService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 应用管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/console/api/apps")
@RequiredArgsConstructor
@Validated
@Tag(name = "应用管理", description = "应用的创建、更新、删除、查询等操作")
public class AppController {

    private final AppService appService;

    @Operation(summary = "创建应用", description = "创建一个新的AI应用")
    @PostMapping
    public Result<AppResponse> createApp(
            @Parameter(description = "租户ID", required = true) @RequestHeader("X-Tenant-Id") @NotBlank String tenantId,
            @Parameter(description = "应用创建请求", required = true) @RequestBody @Valid AppCreateRequest request) {
        
        AppResponse response = appService.createApp(tenantId, request);
        return Result.success(response);
    }

    @Operation(summary = "更新应用", description = "更新指定应用的信息")
    @PutMapping("/{appId}")
    public Result<AppResponse> updateApp(
            @Parameter(description = "租户ID", required = true) @RequestHeader("X-Tenant-Id") @NotBlank String tenantId,
            @Parameter(description = "应用ID", required = true) @PathVariable @NotBlank String appId,
            @Parameter(description = "应用更新请求", required = true) @RequestBody @Valid AppUpdateRequest request) {
        
        AppResponse response = appService.updateApp(tenantId, appId, request);
        return Result.success(response);
    }

    @Operation(summary = "删除应用", description = "删除指定的应用")
    @DeleteMapping("/{appId}")
    public Result<Void> deleteApp(
            @Parameter(description = "租户ID", required = true) @RequestHeader("X-Tenant-Id") @NotBlank String tenantId,
            @Parameter(description = "应用ID", required = true) @PathVariable @NotBlank String appId) {
        
        appService.deleteApp(tenantId, appId);
        return Result.success();
    }

    @Operation(summary = "获取应用详情", description = "根据应用ID获取应用的详细信息")
    @GetMapping("/{appId}")
    public Result<AppResponse> getApp(
            @Parameter(description = "租户ID", required = true) @RequestHeader("X-Tenant-Id") @NotBlank String tenantId,
            @Parameter(description = "应用ID", required = true) @PathVariable @NotBlank String appId) {
        
        AppResponse response = appService.getApp(tenantId, appId);
        return Result.success(response);
    }

    @Operation(summary = "获取应用列表", description = "分页获取租户下的应用列表")
    @GetMapping
    public Result<IPage<AppResponse>> getApps(
            @Parameter(description = "租户ID", required = true) @RequestHeader("X-Tenant-Id") @NotBlank String tenantId,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") @Min(1) int page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") @Min(1) int size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        IPage<AppResponse> response;
        if (keyword != null && !keyword.trim().isEmpty()) {
            response = appService.searchApps(tenantId, keyword.trim(), page, size);
        } else {
            response = appService.getApps(tenantId, page, size);
        }
        
        return Result.success(response);
    }

    @Operation(summary = "发布应用", description = "将应用状态设置为已发布")
    @PostMapping("/{appId}/publish")
    public Result<AppResponse> publishApp(
            @Parameter(description = "租户ID", required = true) @RequestHeader("X-Tenant-Id") @NotBlank String tenantId,
            @Parameter(description = "应用ID", required = true) @PathVariable @NotBlank String appId) {
        
        AppResponse response = appService.publishApp(tenantId, appId);
        return Result.success(response);
    }

    @Operation(summary = "禁用应用", description = "将应用状态设置为已禁用")
    @PostMapping("/{appId}/disable")
    public Result<AppResponse> disableApp(
            @Parameter(description = "租户ID", required = true) @RequestHeader("X-Tenant-Id") @NotBlank String tenantId,
            @Parameter(description = "应用ID", required = true) @PathVariable @NotBlank String appId) {
        
        AppResponse response = appService.disableApp(tenantId, appId);
        return Result.success(response);
    }
}
