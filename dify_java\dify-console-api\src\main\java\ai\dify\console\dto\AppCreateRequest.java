package ai.dify.console.dto;

import ai.dify.domain.enums.AppMode;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 应用创建请求DTO
 */
@Data
public class AppCreateRequest {

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用图标
     */
    private String icon;

    /**
     * 应用图标背景色
     */
    private String iconBackground;

    /**
     * 应用模式
     */
    @NotNull(message = "应用模式不能为空")
    private AppMode mode;

    /**
     * 应用配置
     */
    private Map<String, Object> appModelConfig;

    /**
     * 是否启用站点
     */
    private Boolean enableSite = false;

    /**
     * 是否启用API
     */
    private Boolean enableApi = false;

    /**
     * 开场白
     */
    private String openingStatement;

    /**
     * 建议问题
     */
    private String[] suggestedQuestions;

    /**
     * 用户输入表单
     */
    private Map<String, Object> userInputForm;

    /**
     * 是否为模板
     */
    private Boolean isTemplate = false;

    /**
     * 是否为公开模板
     */
    private Boolean isPublic = false;
}
