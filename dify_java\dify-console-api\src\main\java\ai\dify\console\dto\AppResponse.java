package ai.dify.console.dto;

import ai.dify.domain.enums.AppMode;
import ai.dify.domain.enums.AppStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 应用响应DTO
 */
@Data
public class AppResponse {

    /**
     * 应用ID
     */
    private String id;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用图标
     */
    private String icon;

    /**
     * 应用图标背景色
     */
    private String iconBackground;

    /**
     * 应用模式
     */
    private AppMode mode;

    /**
     * 应用状态
     */
    private AppStatus status;

    /**
     * 是否启用站点
     */
    private Boolean enableSite;

    /**
     * 是否启用API
     */
    private Boolean enableApi;

    /**
     * API RPM限制
     */
    private Integer apiRpm;

    /**
     * API RPM限制启用状态
     */
    private Boolean apiRpmEnabled;

    /**
     * API TPM限制
     */
    private Integer apiTpm;

    /**
     * API TPM限制启用状态
     */
    private Boolean apiTpmEnabled;

    /**
     * 应用配置
     */
    private Map<String, Object> appModelConfig;

    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 最大对话轮数
     */
    private Integer maxConversationLength;

    /**
     * 开场白
     */
    private String openingStatement;

    /**
     * 建议问题
     */
    private String[] suggestedQuestions;

    /**
     * 建议问题启用状态
     */
    private Boolean suggestedQuestionsAfterAnswerEnabled;

    /**
     * 语音转文本启用状态
     */
    private Boolean speechToTextEnabled;

    /**
     * 文本转语音启用状态
     */
    private Boolean textToSpeechEnabled;

    /**
     * 更多类似问题启用状态
     */
    private Boolean moreLikeThisEnabled;

    /**
     * 用户输入表单
     */
    private Map<String, Object> userInputForm;

    /**
     * 是否为模板
     */
    private Boolean isTemplate;

    /**
     * 是否为公开模板
     */
    private Boolean isPublic;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    private String createdBy;

    /**
     * 更新者ID
     */
    private String updatedBy;
}
