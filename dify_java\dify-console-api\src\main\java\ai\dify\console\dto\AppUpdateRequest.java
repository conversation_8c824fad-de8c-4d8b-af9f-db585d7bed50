package ai.dify.console.dto;

import lombok.Data;

import java.util.Map;

/**
 * 应用更新请求DTO
 */
@Data
public class AppUpdateRequest {

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用图标
     */
    private String icon;

    /**
     * 应用图标背景色
     */
    private String iconBackground;

    /**
     * 应用配置
     */
    private Map<String, Object> appModelConfig;

    /**
     * 是否启用站点
     */
    private Boolean enableSite;

    /**
     * 是否启用API
     */
    private Boolean enableApi;

    /**
     * API RPM限制
     */
    private Integer apiRpm;

    /**
     * API RPM限制启用状态
     */
    private Boolean apiRpmEnabled;

    /**
     * API TPM限制
     */
    private Integer apiTpm;

    /**
     * API TPM限制启用状态
     */
    private Boolean apiTpmEnabled;

    /**
     * 最大对话轮数
     */
    private Integer maxConversationLength;

    /**
     * 开场白
     */
    private String openingStatement;

    /**
     * 建议问题
     */
    private String[] suggestedQuestions;

    /**
     * 建议问题启用状态
     */
    private Boolean suggestedQuestionsAfterAnswerEnabled;

    /**
     * 语音转文本启用状态
     */
    private Boolean speechToTextEnabled;

    /**
     * 文本转语音启用状态
     */
    private Boolean textToSpeechEnabled;

    /**
     * 更多类似问题启用状态
     */
    private Boolean moreLikeThisEnabled;

    /**
     * 用户输入表单
     */
    private Map<String, Object> userInputForm;

    /**
     * 是否为模板
     */
    private Boolean isTemplate;

    /**
     * 是否为公开模板
     */
    private Boolean isPublic;
}
