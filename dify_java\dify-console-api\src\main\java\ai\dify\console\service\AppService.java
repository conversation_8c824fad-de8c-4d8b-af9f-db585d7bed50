package ai.dify.console.service;

import ai.dify.console.dto.AppCreateRequest;
import ai.dify.console.dto.AppResponse;
import ai.dify.console.dto.AppUpdateRequest;
import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.domain.entity.App;
import ai.dify.domain.enums.AppStatus;
import ai.dify.domain.repository.AppRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppService {

    private final AppRepository appRepository;

    /**
     * 创建应用
     */
    @Transactional
    public AppResponse createApp(String tenantId, AppCreateRequest request) {
        log.info("创建应用: tenantId={}, name={}", tenantId, request.getName());

        // 检查应用名称是否已存在
        App existingApp = appRepository.findByTenantIdAndName(tenantId, request.getName());
        if (existingApp != null) {
            throw new DifyException(ResultCode.CONFLICT, "应用名称已存在");
        }

        // 创建应用实体
        App app = new App();
        BeanUtils.copyProperties(request, app);
        app.setTenantId(tenantId);
        app.setStatus(AppStatus.DRAFT);

        // 保存应用
        appRepository.insert(app);

        log.info("应用创建成功: id={}", app.getId());
        return convertToResponse(app);
    }

    /**
     * 更新应用
     */
    @Transactional
    public AppResponse updateApp(String tenantId, String appId, AppUpdateRequest request) {
        log.info("更新应用: tenantId={}, appId={}", tenantId, appId);

        // 查询应用
        App app = getAppByIdAndTenantId(appId, tenantId);

        // 如果更新名称，检查是否重复
        if (request.getName() != null && !request.getName().equals(app.getName())) {
            App existingApp = appRepository.findByTenantIdAndName(tenantId, request.getName());
            if (existingApp != null && !existingApp.getId().equals(appId)) {
                throw new DifyException(ResultCode.CONFLICT, "应用名称已存在");
            }
        }

        // 更新应用属性
        updateAppProperties(app, request);

        // 保存更新
        appRepository.updateById(app);

        log.info("应用更新成功: id={}", app.getId());
        return convertToResponse(app);
    }

    /**
     * 删除应用
     */
    @Transactional
    public void deleteApp(String tenantId, String appId) {
        log.info("删除应用: tenantId={}, appId={}", tenantId, appId);

        // 查询应用
        App app = getAppByIdAndTenantId(appId, tenantId);

        // 软删除
        app.setStatus(AppStatus.DELETED);
        appRepository.updateById(app);

        log.info("应用删除成功: id={}", app.getId());
    }

    /**
     * 获取应用详情
     */
    public AppResponse getApp(String tenantId, String appId) {
        App app = getAppByIdAndTenantId(appId, tenantId);
        return convertToResponse(app);
    }

    /**
     * 分页查询应用列表
     */
    public IPage<AppResponse> getApps(String tenantId, int page, int size) {
        Page<App> pageRequest = new Page<>(page, size);
        IPage<App> appPage = appRepository.findByTenantId(pageRequest, tenantId);
        
        return appPage.convert(this::convertToResponse);
    }

    /**
     * 搜索应用
     */
    public IPage<AppResponse> searchApps(String tenantId, String keyword, int page, int size) {
        Page<App> pageRequest = new Page<>(page, size);
        IPage<App> appPage = appRepository.searchByKeyword(pageRequest, tenantId, keyword);
        
        return appPage.convert(this::convertToResponse);
    }

    /**
     * 发布应用
     */
    @Transactional
    public AppResponse publishApp(String tenantId, String appId) {
        log.info("发布应用: tenantId={}, appId={}", tenantId, appId);

        App app = getAppByIdAndTenantId(appId, tenantId);
        app.setStatus(AppStatus.PUBLISHED);
        appRepository.updateById(app);

        log.info("应用发布成功: id={}", app.getId());
        return convertToResponse(app);
    }

    /**
     * 禁用应用
     */
    @Transactional
    public AppResponse disableApp(String tenantId, String appId) {
        log.info("禁用应用: tenantId={}, appId={}", tenantId, appId);

        App app = getAppByIdAndTenantId(appId, tenantId);
        app.setStatus(AppStatus.DISABLED);
        appRepository.updateById(app);

        log.info("应用禁用成功: id={}", app.getId());
        return convertToResponse(app);
    }

    /**
     * 根据ID和租户ID获取应用
     */
    private App getAppByIdAndTenantId(String appId, String tenantId) {
        App app = appRepository.selectById(appId);
        if (app == null || !app.getTenantId().equals(tenantId) || app.getStatus() == AppStatus.DELETED) {
            throw new DifyException(ResultCode.APP_NOT_FOUND);
        }
        return app;
    }

    /**
     * 更新应用属性
     */
    private void updateAppProperties(App app, AppUpdateRequest request) {
        if (request.getName() != null) {
            app.setName(request.getName());
        }
        if (request.getDescription() != null) {
            app.setDescription(request.getDescription());
        }
        if (request.getIcon() != null) {
            app.setIcon(request.getIcon());
        }
        if (request.getIconBackground() != null) {
            app.setIconBackground(request.getIconBackground());
        }
        if (request.getAppModelConfig() != null) {
            app.setAppModelConfig(request.getAppModelConfig());
        }
        if (request.getEnableSite() != null) {
            app.setEnableSite(request.getEnableSite());
        }
        if (request.getEnableApi() != null) {
            app.setEnableApi(request.getEnableApi());
        }
        if (request.getApiRpm() != null) {
            app.setApiRpm(request.getApiRpm());
        }
        if (request.getApiRpmEnabled() != null) {
            app.setApiRpmEnabled(request.getApiRpmEnabled());
        }
        if (request.getApiTpm() != null) {
            app.setApiTpm(request.getApiTpm());
        }
        if (request.getApiTpmEnabled() != null) {
            app.setApiTpmEnabled(request.getApiTpmEnabled());
        }
        if (request.getMaxConversationLength() != null) {
            app.setMaxConversationLength(request.getMaxConversationLength());
        }
        if (request.getOpeningStatement() != null) {
            app.setOpeningStatement(request.getOpeningStatement());
        }
        if (request.getSuggestedQuestions() != null) {
            app.setSuggestedQuestions(request.getSuggestedQuestions());
        }
        if (request.getSuggestedQuestionsAfterAnswerEnabled() != null) {
            app.setSuggestedQuestionsAfterAnswerEnabled(request.getSuggestedQuestionsAfterAnswerEnabled());
        }
        if (request.getSpeechToTextEnabled() != null) {
            app.setSpeechToTextEnabled(request.getSpeechToTextEnabled());
        }
        if (request.getTextToSpeechEnabled() != null) {
            app.setTextToSpeechEnabled(request.getTextToSpeechEnabled());
        }
        if (request.getMoreLikeThisEnabled() != null) {
            app.setMoreLikeThisEnabled(request.getMoreLikeThisEnabled());
        }
        if (request.getUserInputForm() != null) {
            app.setUserInputForm(request.getUserInputForm());
        }
        if (request.getIsTemplate() != null) {
            app.setIsTemplate(request.getIsTemplate());
        }
        if (request.getIsPublic() != null) {
            app.setIsPublic(request.getIsPublic());
        }
    }

    /**
     * 转换为响应DTO
     */
    private AppResponse convertToResponse(App app) {
        AppResponse response = new AppResponse();
        BeanUtils.copyProperties(app, response);
        return response;
    }
}
