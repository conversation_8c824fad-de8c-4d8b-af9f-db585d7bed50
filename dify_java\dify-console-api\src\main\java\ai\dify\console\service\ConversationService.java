package ai.dify.console.service;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.domain.entity.Conversation;
import ai.dify.domain.entity.Message;
import ai.dify.domain.repository.ConversationRepository;
import ai.dify.domain.repository.MessageRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 对话服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConversationService {

    private final ConversationRepository conversationRepository;
    private final MessageRepository messageRepository;

    /**
     * 创建对话
     */
    @Transactional
    public Conversation createConversation(String appId, String endUserId, String name, Map<String, Object> inputs) {
        log.info("创建对话: appId={}, endUserId={}, name={}", appId, endUserId, name);

        Conversation conversation = new Conversation();
        conversation.setAppId(appId);
        conversation.setFromEndUserId(endUserId);
        conversation.setName(name);
        conversation.setInputs(inputs);
        conversation.setMode("chat");
        conversation.setStatus("normal");
        conversation.setFromSource("api");
        conversation.setIsPinned(false);

        conversationRepository.insert(conversation);

        log.info("对话创建成功: id={}", conversation.getId());
        return conversation;
    }

    /**
     * 更新对话
     */
    @Transactional
    public Conversation updateConversation(String conversationId, String name, String summary) {
        log.info("更新对话: conversationId={}, name={}", conversationId, name);

        Conversation conversation = getConversationById(conversationId);

        if (name != null) {
            conversation.setName(name);
        }
        if (summary != null) {
            conversation.setSummary(summary);
        }

        conversationRepository.updateById(conversation);

        log.info("对话更新成功: id={}", conversation.getId());
        return conversation;
    }

    /**
     * 删除对话
     */
    @Transactional
    public void deleteConversation(String conversationId) {
        log.info("删除对话: conversationId={}", conversationId);

        Conversation conversation = getConversationById(conversationId);
        conversationRepository.deleteById(conversationId);

        // 同时删除对话下的所有消息
        List<Message> messages = messageRepository.findByConversationId(conversationId);
        for (Message message : messages) {
            messageRepository.deleteById(message.getId());
        }

        log.info("对话删除成功: id={}", conversationId);
    }

    /**
     * 获取对话详情
     */
    public Conversation getConversation(String conversationId) {
        return getConversationById(conversationId);
    }

    /**
     * 根据应用ID分页查询对话
     */
    public IPage<Conversation> getConversationsByAppId(String appId, int page, int size) {
        Page<Conversation> pageRequest = new Page<>(page, size);
        return conversationRepository.findByAppId(pageRequest, appId);
    }

    /**
     * 根据应用ID和终端用户ID查询对话
     */
    public IPage<Conversation> getConversationsByAppIdAndEndUserId(String appId, String endUserId, int page, int size) {
        Page<Conversation> pageRequest = new Page<>(page, size);
        return conversationRepository.findByAppIdAndEndUserId(pageRequest, appId, endUserId);
    }

    /**
     * 根据终端用户ID查询对话
     */
    public List<Conversation> getConversationsByEndUserId(String endUserId) {
        return conversationRepository.findByEndUserId(endUserId);
    }

    /**
     * 置顶对话
     */
    @Transactional
    public Conversation pinConversation(String conversationId) {
        log.info("置顶对话: conversationId={}", conversationId);

        Conversation conversation = getConversationById(conversationId);
        conversation.setIsPinned(true);
        conversationRepository.updateById(conversation);

        log.info("对话置顶成功: id={}", conversationId);
        return conversation;
    }

    /**
     * 取消置顶对话
     */
    @Transactional
    public Conversation unpinConversation(String conversationId) {
        log.info("取消置顶对话: conversationId={}", conversationId);

        Conversation conversation = getConversationById(conversationId);
        conversation.setIsPinned(false);
        conversationRepository.updateById(conversation);

        log.info("对话取消置顶成功: id={}", conversationId);
        return conversation;
    }

    /**
     * 标记对话为已读
     */
    @Transactional
    public Conversation markAsRead(String conversationId, String accountId) {
        log.info("标记对话为已读: conversationId={}, accountId={}", conversationId, accountId);

        Conversation conversation = getConversationById(conversationId);
        conversation.setReadAt(LocalDateTime.now());
        conversation.setReadAccountId(accountId);
        conversationRepository.updateById(conversation);

        log.info("对话标记为已读成功: id={}", conversationId);
        return conversation;
    }

    /**
     * 根据应用ID查询置顶对话
     */
    public List<Conversation> getPinnedConversations(String appId) {
        return conversationRepository.findPinnedByAppId(appId);
    }

    /**
     * 根据应用ID查询最近活跃的对话
     */
    public List<Conversation> getRecentActiveConversations(String appId, Integer limit) {
        return conversationRepository.findRecentActiveByAppId(appId, limit);
    }

    /**
     * 搜索对话
     */
    public IPage<Conversation> searchConversations(String appId, String keyword, int page, int size) {
        Page<Conversation> pageRequest = new Page<>(page, size);
        return conversationRepository.searchByName(pageRequest, appId, keyword);
    }

    /**
     * 统计应用下的对话数量
     */
    public Long countConversationsByAppId(String appId) {
        return conversationRepository.countByAppId(appId);
    }

    /**
     * 统计终端用户的对话数量
     */
    public Long countConversationsByEndUserId(String endUserId) {
        return conversationRepository.countByEndUserId(endUserId);
    }

    /**
     * 根据ID获取对话
     */
    private Conversation getConversationById(String conversationId) {
        Conversation conversation = conversationRepository.selectById(conversationId);
        if (conversation == null) {
            throw new DifyException(ResultCode.NOT_FOUND, "对话不存在");
        }
        return conversation;
    }
}
