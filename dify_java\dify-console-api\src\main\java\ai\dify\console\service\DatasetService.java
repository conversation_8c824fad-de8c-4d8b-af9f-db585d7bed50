package ai.dify.console.service;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.domain.entity.Dataset;
import ai.dify.domain.enums.DatasetStatus;
import ai.dify.domain.repository.DatasetRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据集服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DatasetService {

    private final DatasetRepository datasetRepository;

    /**
     * 创建数据集
     */
    @Transactional
    public Dataset createDataset(String tenantId, String name, String description) {
        log.info("创建数据集: tenantId={}, name={}", tenantId, name);

        // 检查数据集名称是否已存在
        Dataset existingDataset = datasetRepository.findByTenantIdAndName(tenantId, name);
        if (existingDataset != null) {
            throw new DifyException(ResultCode.CONFLICT, "数据集名称已存在");
        }

        // 创建数据集实体
        Dataset dataset = new Dataset();
        dataset.setTenantId(tenantId);
        dataset.setName(name);
        dataset.setDescription(description);
        dataset.setStatus(DatasetStatus.AVAILABLE);
        dataset.setDataSourceType("upload_file");
        dataset.setIndexStruct("high_quality");
        dataset.setVectorStore("weaviate");
        dataset.setDocumentCount(0);
        dataset.setCharacterCount(0L);
        dataset.setSegmentCount(0);
        dataset.setPermission("only_me");

        // 保存数据集
        datasetRepository.insert(dataset);

        log.info("数据集创建成功: id={}", dataset.getId());
        return dataset;
    }

    /**
     * 更新数据集
     */
    @Transactional
    public Dataset updateDataset(String tenantId, String datasetId, String name, String description) {
        log.info("更新数据集: tenantId={}, datasetId={}", tenantId, datasetId);

        // 查询数据集
        Dataset dataset = getDatasetByIdAndTenantId(datasetId, tenantId);

        // 如果更新名称，检查是否重复
        if (name != null && !name.equals(dataset.getName())) {
            Dataset existingDataset = datasetRepository.findByTenantIdAndName(tenantId, name);
            if (existingDataset != null && !existingDataset.getId().equals(datasetId)) {
                throw new DifyException(ResultCode.CONFLICT, "数据集名称已存在");
            }
            dataset.setName(name);
        }

        if (description != null) {
            dataset.setDescription(description);
        }

        // 保存更新
        datasetRepository.updateById(dataset);

        log.info("数据集更新成功: id={}", dataset.getId());
        return dataset;
    }

    /**
     * 删除数据集
     */
    @Transactional
    public void deleteDataset(String tenantId, String datasetId) {
        log.info("删除数据集: tenantId={}, datasetId={}", tenantId, datasetId);

        // 查询数据集
        Dataset dataset = getDatasetByIdAndTenantId(datasetId, tenantId);

        // 软删除
        dataset.setStatus(DatasetStatus.DELETED);
        datasetRepository.updateById(dataset);

        log.info("数据集删除成功: id={}", dataset.getId());
    }

    /**
     * 获取数据集详情
     */
    public Dataset getDataset(String tenantId, String datasetId) {
        return getDatasetByIdAndTenantId(datasetId, tenantId);
    }

    /**
     * 分页查询数据集列表
     */
    public IPage<Dataset> getDatasets(String tenantId, int page, int size) {
        Page<Dataset> pageRequest = new Page<>(page, size);
        return datasetRepository.findByTenantId(pageRequest, tenantId);
    }

    /**
     * 搜索数据集
     */
    public IPage<Dataset> searchDatasets(String tenantId, String keyword, int page, int size) {
        Page<Dataset> pageRequest = new Page<>(page, size);
        return datasetRepository.searchByKeyword(pageRequest, tenantId, keyword);
    }

    /**
     * 根据状态查询数据集
     */
    public List<Dataset> getDatasetsByStatus(String tenantId, DatasetStatus status) {
        return datasetRepository.findByTenantIdAndStatus(tenantId, status);
    }

    /**
     * 统计数据集数量
     */
    public Long countDatasets(String tenantId) {
        return datasetRepository.countByTenantId(tenantId);
    }

    /**
     * 根据ID和租户ID获取数据集
     */
    private Dataset getDatasetByIdAndTenantId(String datasetId, String tenantId) {
        Dataset dataset = datasetRepository.selectById(datasetId);
        if (dataset == null || !dataset.getTenantId().equals(tenantId) || dataset.getStatus() == DatasetStatus.DELETED) {
            throw new DifyException(ResultCode.DATASET_NOT_FOUND);
        }
        return dataset;
    }
}
