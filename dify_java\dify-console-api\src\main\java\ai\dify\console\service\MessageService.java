package ai.dify.console.service;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.domain.entity.Message;
import ai.dify.domain.repository.MessageRepository;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageService {

    private final MessageRepository messageRepository;

    /**
     * 创建消息
     */
    @Transactional
    public Message createMessage(String appId, String conversationId, String query, 
                               Map<String, Object> inputs, String fromEndUserId) {
        log.info("创建消息: appId={}, conversationId={}, query={}", appId, conversationId, query);

        Message message = new Message();
        message.setAppId(appId);
        message.setConversationId(conversationId);
        message.setQuery(query);
        message.setInputs(inputs);
        message.setFromEndUserId(fromEndUserId);
        message.setFromSource("api");
        message.setStatus("normal");

        messageRepository.insert(message);

        log.info("消息创建成功: id={}", message.getId());
        return message;
    }

    /**
     * 更新消息回答
     */
    @Transactional
    public Message updateMessageAnswer(String messageId, String answer, Integer answerTokens, 
                                     Double totalPrice, String currency, Map<String, Object> metadata) {
        log.info("更新消息回答: messageId={}", messageId);

        Message message = getMessageById(messageId);
        message.setAnswer(answer);
        message.setAnswerTokens(answerTokens);
        message.setTotalPrice(totalPrice);
        message.setCurrency(currency);
        message.setMessageMetadata(metadata);

        messageRepository.updateById(message);

        log.info("消息回答更新成功: id={}", messageId);
        return message;
    }

    /**
     * 更新消息状态
     */
    @Transactional
    public Message updateMessageStatus(String messageId, String status, String error) {
        log.info("更新消息状态: messageId={}, status={}", messageId, status);

        Message message = getMessageById(messageId);
        message.setStatus(status);
        if (error != null) {
            message.setError(error);
        }

        messageRepository.updateById(message);

        log.info("消息状态更新成功: id={}", messageId);
        return message;
    }

    /**
     * 获取消息详情
     */
    public Message getMessage(String messageId) {
        return getMessageById(messageId);
    }

    /**
     * 根据对话ID分页查询消息
     */
    public IPage<Message> getMessagesByConversationId(String conversationId, int page, int size) {
        Page<Message> pageRequest = new Page<>(page, size);
        return messageRepository.findByConversationId(pageRequest, conversationId);
    }

    /**
     * 根据对话ID查询消息列表
     */
    public List<Message> getMessagesByConversationId(String conversationId) {
        return messageRepository.findByConversationId(conversationId);
    }

    /**
     * 根据应用ID分页查询消息
     */
    public IPage<Message> getMessagesByAppId(String appId, int page, int size) {
        Page<Message> pageRequest = new Page<>(page, size);
        return messageRepository.findByAppId(pageRequest, appId);
    }

    /**
     * 根据终端用户ID查询消息
     */
    public IPage<Message> getMessagesByEndUserId(String endUserId, int page, int size) {
        Page<Message> pageRequest = new Page<>(page, size);
        return messageRepository.findByEndUserId(pageRequest, endUserId);
    }

    /**
     * 根据工作流运行ID查询消息
     */
    public List<Message> getMessagesByWorkflowRunId(String workflowRunId) {
        return messageRepository.findByWorkflowRunId(workflowRunId);
    }

    /**
     * 根据父消息ID查询子消息
     */
    public List<Message> getMessagesByParentMessageId(String parentMessageId) {
        return messageRepository.findByParentMessageId(parentMessageId);
    }

    /**
     * 获取对话中的最新消息
     */
    public Message getLatestMessageByConversationId(String conversationId) {
        return messageRepository.findLatestByConversationId(conversationId);
    }

    /**
     * 根据状态查询消息
     */
    public List<Message> getMessagesByStatus(String status) {
        return messageRepository.findByStatus(status);
    }

    /**
     * 根据应用ID和时间范围查询消息
     */
    public List<Message> getMessagesByAppIdAndTimeRange(String appId, LocalDateTime startTime, LocalDateTime endTime) {
        return messageRepository.findByAppIdAndTimeRange(appId, startTime, endTime);
    }

    /**
     * 统计应用下的消息数量
     */
    public Long countMessagesByAppId(String appId) {
        return messageRepository.countByAppId(appId);
    }

    /**
     * 统计对话下的消息数量
     */
    public Long countMessagesByConversationId(String conversationId) {
        return messageRepository.countByConversationId(conversationId);
    }

    /**
     * 根据应用ID和日期统计消息数量
     */
    public Long countMessagesByAppIdAndDate(String appId, String date) {
        return messageRepository.countByAppIdAndDate(appId, date);
    }

    /**
     * 删除消息
     */
    @Transactional
    public void deleteMessage(String messageId) {
        log.info("删除消息: messageId={}", messageId);

        Message message = getMessageById(messageId);
        messageRepository.deleteById(messageId);

        log.info("消息删除成功: id={}", messageId);
    }

    /**
     * 根据ID获取消息
     */
    private Message getMessageById(String messageId) {
        Message message = messageRepository.selectById(messageId);
        if (message == null) {
            throw new DifyException(ResultCode.NOT_FOUND, "消息不存在");
        }
        return message;
    }
}
