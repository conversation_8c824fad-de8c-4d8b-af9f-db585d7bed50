server:
  port: 5001
  servlet:
    context-path: /console

spring:
  application:
    name: dify-console-api
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *************************************
    username: postgres
    password: difyai123456
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  redis:
    host: localhost
    port: 6379
    password: difyai123456
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_uuid
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*.xml

# 日志配置
logging:
  level:
    ai.dify: DEBUG
    org.springframework.security: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/dify-console-api.log

# Swagger配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: ai.dify.console.controller

# 应用配置
dify:
  # 安全配置
  security:
    jwt:
      secret: dify-java-secret-key-2024
      expiration: 86400000 # 24小时
    cors:
      allowed-origins: 
        - http://localhost:3000
        - http://localhost:3001
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"
      allow-credentials: true
  
  # 文件上传配置
  file:
    upload:
      max-size: 100MB
      allowed-types:
        - pdf
        - txt
        - md
        - docx
        - html
        - json
        - csv
      storage-path: /tmp/dify/uploads
  
  # 模型配置
  model:
    default-provider: openai
    providers:
      openai:
        api-key: ${OPENAI_API_KEY:}
        base-url: https://api.openai.com/v1
        timeout: 60000
      anthropic:
        api-key: ${ANTHROPIC_API_KEY:}
        base-url: https://api.anthropic.com
        timeout: 60000
  
  # 向量数据库配置
  vector:
    provider: weaviate
    weaviate:
      host: localhost
      port: 8080
      scheme: http
    qdrant:
      host: localhost
      port: 6333
      api-key: ${QDRANT_API_KEY:}
  
  # 嵌入配置
  embedding:
    provider: openai
    model: text-embedding-3-small
    dimension: 1536
    batch-size: 100

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: *************************************_dev
  
  redis:
    database: 1

logging:
  level:
    root: INFO
    ai.dify: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: ******************************************
  
  redis:
    database: 2

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:dify}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:difyai123456}
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:difyai123456}
    database: ${REDIS_DB:0}

logging:
  level:
    root: WARN
    ai.dify: INFO
  file:
    name: /var/log/dify/console-api.log
