package ai.dify.console.service;

import ai.dify.common.exception.DifyException;
import ai.dify.console.dto.AppCreateRequest;
import ai.dify.console.dto.AppResponse;
import ai.dify.domain.entity.App;
import ai.dify.domain.enums.AppMode;
import ai.dify.domain.enums.AppStatus;
import ai.dify.domain.repository.AppRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 应用服务测试
 */
@ExtendWith(MockitoExtension.class)
class AppServiceTest {

    @Mock
    private AppRepository appRepository;

    @InjectMocks
    private AppService appService;

    private String tenantId;
    private AppCreateRequest createRequest;
    private App app;

    @BeforeEach
    void setUp() {
        tenantId = "tenant-123";
        
        createRequest = new AppCreateRequest();
        createRequest.setName("Test App");
        createRequest.setDescription("Test Description");
        createRequest.setMode(AppMode.CHAT);
        createRequest.setEnableSite(true);
        createRequest.setEnableApi(true);

        app = new App();
        app.setId("app-123");
        app.setTenantId(tenantId);
        app.setName("Test App");
        app.setDescription("Test Description");
        app.setMode(AppMode.CHAT);
        app.setStatus(AppStatus.DRAFT);
        app.setEnableSite(true);
        app.setEnableApi(true);
    }

    @Test
    void createApp_Success() {
        // Given
        when(appRepository.findByTenantIdAndName(tenantId, createRequest.getName())).thenReturn(null);
        when(appRepository.insert(any(App.class))).thenReturn(1);

        // When
        AppResponse response = appService.createApp(tenantId, createRequest);

        // Then
        assertNotNull(response);
        assertEquals(createRequest.getName(), response.getName());
        assertEquals(createRequest.getDescription(), response.getDescription());
        assertEquals(createRequest.getMode(), response.getMode());
        assertEquals(AppStatus.DRAFT, response.getStatus());
        
        verify(appRepository).findByTenantIdAndName(tenantId, createRequest.getName());
        verify(appRepository).insert(any(App.class));
    }

    @Test
    void createApp_NameAlreadyExists_ThrowsException() {
        // Given
        when(appRepository.findByTenantIdAndName(tenantId, createRequest.getName())).thenReturn(app);

        // When & Then
        DifyException exception = assertThrows(DifyException.class, 
            () -> appService.createApp(tenantId, createRequest));
        
        assertEquals("应用名称已存在", exception.getMessage());
        verify(appRepository).findByTenantIdAndName(tenantId, createRequest.getName());
        verify(appRepository, never()).insert(any(App.class));
    }

    @Test
    void getApp_Success() {
        // Given
        when(appRepository.selectById(app.getId())).thenReturn(app);

        // When
        AppResponse response = appService.getApp(tenantId, app.getId());

        // Then
        assertNotNull(response);
        assertEquals(app.getId(), response.getId());
        assertEquals(app.getName(), response.getName());
        assertEquals(app.getDescription(), response.getDescription());
        
        verify(appRepository).selectById(app.getId());
    }

    @Test
    void getApp_NotFound_ThrowsException() {
        // Given
        when(appRepository.selectById(anyString())).thenReturn(null);

        // When & Then
        assertThrows(DifyException.class, 
            () -> appService.getApp(tenantId, "non-existent-id"));
        
        verify(appRepository).selectById("non-existent-id");
    }

    @Test
    void getApp_WrongTenant_ThrowsException() {
        // Given
        app.setTenantId("other-tenant");
        when(appRepository.selectById(app.getId())).thenReturn(app);

        // When & Then
        assertThrows(DifyException.class, 
            () -> appService.getApp(tenantId, app.getId()));
        
        verify(appRepository).selectById(app.getId());
    }

    @Test
    void deleteApp_Success() {
        // Given
        when(appRepository.selectById(app.getId())).thenReturn(app);
        when(appRepository.updateById(any(App.class))).thenReturn(1);

        // When
        appService.deleteApp(tenantId, app.getId());

        // Then
        verify(appRepository).selectById(app.getId());
        verify(appRepository).updateById(argThat(updatedApp -> 
            AppStatus.DELETED.equals(updatedApp.getStatus())));
    }

    @Test
    void publishApp_Success() {
        // Given
        when(appRepository.selectById(app.getId())).thenReturn(app);
        when(appRepository.updateById(any(App.class))).thenReturn(1);

        // When
        AppResponse response = appService.publishApp(tenantId, app.getId());

        // Then
        assertNotNull(response);
        assertEquals(AppStatus.PUBLISHED, response.getStatus());
        
        verify(appRepository).selectById(app.getId());
        verify(appRepository).updateById(argThat(updatedApp -> 
            AppStatus.PUBLISHED.equals(updatedApp.getStatus())));
    }

    @Test
    void disableApp_Success() {
        // Given
        when(appRepository.selectById(app.getId())).thenReturn(app);
        when(appRepository.updateById(any(App.class))).thenReturn(1);

        // When
        AppResponse response = appService.disableApp(tenantId, app.getId());

        // Then
        assertNotNull(response);
        assertEquals(AppStatus.DISABLED, response.getStatus());
        
        verify(appRepository).selectById(app.getId());
        verify(appRepository).updateById(argThat(updatedApp -> 
            AppStatus.DISABLED.equals(updatedApp.getStatus())));
    }
}
