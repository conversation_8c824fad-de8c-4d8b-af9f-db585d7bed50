package ai.dify.domain.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis Plus配置
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        return interceptor;
    }

    /**
     * 自动填充处理器
     */
    @Component
    public static class AutoFillMetaObjectHandler implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            LocalDateTime now = LocalDateTime.now();
            
            // 填充创建时间
            this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, now);
            // 填充更新时间
            this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, now);
            
            // TODO: 从当前登录用户获取用户ID
            String currentUserId = getCurrentUserId();
            if (currentUserId != null) {
                // 填充创建者
                this.strictInsertFill(metaObject, "createdBy", String.class, currentUserId);
                // 填充更新者
                this.strictInsertFill(metaObject, "updatedBy", String.class, currentUserId);
            }
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            LocalDateTime now = LocalDateTime.now();
            
            // 填充更新时间
            this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, now);
            
            // TODO: 从当前登录用户获取用户ID
            String currentUserId = getCurrentUserId();
            if (currentUserId != null) {
                // 填充更新者
                this.strictUpdateFill(metaObject, "updatedBy", String.class, currentUserId);
            }
        }

        /**
         * 获取当前用户ID
         * TODO: 实现从Spring Security或其他认证框架获取当前用户ID
         */
        private String getCurrentUserId() {
            // 这里应该从Spring Security Context或其他地方获取当前用户ID
            // 暂时返回null，后续实现认证后再完善
            return null;
        }
    }
}
