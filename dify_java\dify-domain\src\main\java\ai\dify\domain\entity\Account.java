package ai.dify.domain.entity;

import ai.dify.domain.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 账户实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("accounts")
public class Account extends BaseEntity {

    /**
     * 用户名
     */
    private String name;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 密码哈希
     */
    private String passwordHash;

    /**
     * 密码盐
     */
    private String passwordSalt;

    /**
     * 接口语言
     */
    private String interfaceLanguage;

    /**
     * 接口主题
     */
    private String interfaceTheme;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 状态
     */
    private String status;

    /**
     * 初始化向量
     */
    private String initializationVector;

    /**
     * 是否已初始化
     */
    private Boolean initialized;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveAt;
}
