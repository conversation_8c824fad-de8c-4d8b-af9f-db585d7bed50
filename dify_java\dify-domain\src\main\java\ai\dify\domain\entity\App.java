package ai.dify.domain.entity;

import ai.dify.domain.base.BaseEntity;
import ai.dify.domain.enums.AppMode;
import ai.dify.domain.enums.AppStatus;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 应用实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "apps", autoResultMap = true)
public class App extends BaseEntity {

    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用图标
     */
    private String icon;

    /**
     * 应用图标背景色
     */
    private String iconBackground;

    /**
     * 应用模式
     */
    @NotNull(message = "应用模式不能为空")
    private AppMode mode;

    /**
     * 应用状态
     */
    @NotNull(message = "应用状态不能为空")
    private AppStatus status;

    /**
     * 是否启用站点
     */
    private Boolean enableSite;

    /**
     * 是否启用API
     */
    private Boolean enableApi;

    /**
     * API RPM限制
     */
    private Integer apiRpm;

    /**
     * API RPM限制启用状态
     */
    private Boolean apiRpmEnabled;

    /**
     * API TPM限制
     */
    private Integer apiTpm;

    /**
     * API TPM限制启用状态
     */
    private Boolean apiTpmEnabled;

    /**
     * 应用配置（JSON格式）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> appModelConfig;

    /**
     * 工作流ID（当mode为workflow时使用）
     */
    private String workflowId;

    /**
     * 是否使用图标作为答案图标
     */
    private Boolean useIconAsAnswerIcon;

    /**
     * 最大对话轮数
     */
    private Integer maxConversationLength;

    /**
     * 开场白
     */
    private String openingStatement;

    /**
     * 建议问题（JSON数组）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String[] suggestedQuestions;

    /**
     * 建议问题启用状态
     */
    private Boolean suggestedQuestionsAfterAnswerEnabled;

    /**
     * 语音转文本启用状态
     */
    private Boolean speechToTextEnabled;

    /**
     * 文本转语音启用状态
     */
    private Boolean textToSpeechEnabled;

    /**
     * 更多类似问题启用状态
     */
    private Boolean moreLikeThisEnabled;

    /**
     * 用户输入表单（JSON格式）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> userInputForm;

    /**
     * 是否为模板
     */
    private Boolean isTemplate;

    /**
     * 是否为公开模板
     */
    private Boolean isPublic;

    /**
     * 是否为通用模板
     */
    private Boolean isUniversal;
}
