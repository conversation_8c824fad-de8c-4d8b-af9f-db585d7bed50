package ai.dify.domain.entity;

import ai.dify.domain.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 对话实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "conversations", autoResultMap = true)
public class Conversation extends BaseEntity {

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    /**
     * 应用模型配置ID
     */
    private String appModelConfigId;

    /**
     * 模型提供商
     */
    private String modelProvider;

    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 覆盖模型配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> overrideModelConfigs;

    /**
     * 模式
     */
    private String mode;

    /**
     * 对话名称
     */
    private String name;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 输入内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> inputs;

    /**
     * 介绍
     */
    private String introduction;

    /**
     * 系统查询
     */
    private String systemQuery;

    /**
     * 系统查询令牌数
     */
    private Integer systemQueryTokens;

    /**
     * 状态
     */
    private String status;

    /**
     * 来源
     */
    private String fromSource;

    /**
     * 来源账户ID
     */
    private String fromAccountId;

    /**
     * 来源终端用户ID
     */
    private String fromEndUserId;

    /**
     * 读取时间
     */
    private java.time.LocalDateTime readAt;

    /**
     * 读取账户ID
     */
    private String readAccountId;

    /**
     * 是否置顶
     */
    private Boolean isPinned;

    /**
     * 调用来源
     */
    private String invokeFrom;
}
