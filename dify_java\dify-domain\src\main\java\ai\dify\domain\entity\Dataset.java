package ai.dify.domain.entity;

import ai.dify.domain.base.BaseEntity;
import ai.dify.domain.enums.DatasetStatus;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 数据集实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "datasets", autoResultMap = true)
public class Dataset extends BaseEntity {

    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    /**
     * 数据集名称
     */
    @NotBlank(message = "数据集名称不能为空")
    private String name;

    /**
     * 数据集描述
     */
    private String description;

    /**
     * 数据集状态
     */
    @NotNull(message = "数据集状态不能为空")
    private DatasetStatus status;

    /**
     * 数据源类型
     */
    private String dataSourceType;

    /**
     * 索引结构
     */
    private String indexStruct;

    /**
     * 向量存储类型
     */
    private String vectorStore;

    /**
     * 向量存储配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> vectorStoreConfig;

    /**
     * 检索模型配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> retrievalModelConfig;

    /**
     * 文档数量
     */
    private Integer documentCount;

    /**
     * 字符数量
     */
    private Long characterCount;

    /**
     * 分段数量
     */
    private Integer segmentCount;

    /**
     * 嵌入模型
     */
    private String embeddingModel;

    /**
     * 嵌入模型提供商
     */
    private String embeddingModelProvider;

    /**
     * 权限
     */
    private String permission;

    /**
     * 提供商
     */
    private String provider;

    /**
     * 数据集处理规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> datasetProcessRule;

    /**
     * 标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String[] tags;
}
