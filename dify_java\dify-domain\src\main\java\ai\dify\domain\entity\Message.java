package ai.dify.domain.entity;

import ai.dify.domain.base.BaseEntity;
import ai.dify.domain.enums.MessageRole;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 消息实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "messages", autoResultMap = true)
public class Message extends BaseEntity {

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    /**
     * 模型配置ID
     */
    private String modelConfigId;

    /**
     * 对话ID
     */
    @NotBlank(message = "对话ID不能为空")
    private String conversationId;

    /**
     * 输入内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> inputs;

    /**
     * 查询内容
     */
    private String query;

    /**
     * 消息内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> message;

    /**
     * 消息令牌数
     */
    private Integer messageTokens;

    /**
     * 消息单价
     */
    private Double messageUnitPrice;

    /**
     * 消息价格单位
     */
    private String messagePriceUnit;

    /**
     * 回答内容
     */
    private String answer;

    /**
     * 回答令牌数
     */
    private Integer answerTokens;

    /**
     * 回答单价
     */
    private Double answerUnitPrice;

    /**
     * 回答价格单位
     */
    private String answerPriceUnit;

    /**
     * 提供商响应延迟
     */
    private Double providerResponseLatency;

    /**
     * 总令牌数
     */
    private Integer totalTokens;

    /**
     * 总价格
     */
    private Double totalPrice;

    /**
     * 货币
     */
    private String currency;

    /**
     * 来源
     */
    private String fromSource;

    /**
     * 来源账户ID
     */
    private String fromAccountId;

    /**
     * 来源终端用户ID
     */
    private String fromEndUserId;

    /**
     * 状态
     */
    private String status;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 消息元数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> messageMetadata;

    /**
     * 调用深度
     */
    private Integer invokeFrom;

    /**
     * 工作流运行ID
     */
    private String workflowRunId;

    /**
     * 父消息ID
     */
    private String parentMessageId;
}
