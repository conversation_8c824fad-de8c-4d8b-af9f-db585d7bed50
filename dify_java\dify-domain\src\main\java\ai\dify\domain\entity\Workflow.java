package ai.dify.domain.entity;

import ai.dify.domain.base.BaseEntity;
import ai.dify.domain.enums.WorkflowType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 工作流实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "workflows", autoResultMap = true)
public class Workflow extends BaseEntity {

    /**
     * 租户ID
     */
    @NotBlank(message = "租户ID不能为空")
    private String tenantId;

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    /**
     * 工作流类型
     */
    @NotNull(message = "工作流类型不能为空")
    private WorkflowType type;

    /**
     * 版本
     */
    private String version;

    /**
     * 图配置（JSON格式）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> graph;

    /**
     * 特性配置（JSON格式）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> features;

    /**
     * 环境变量（JSON格式）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> environmentVariables;

    /**
     * 对话变量（JSON格式）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> conversationVariables;

    /**
     * 是否已发布
     */
    private Boolean isPublished;

    /**
     * 是否为草稿
     */
    private Boolean isDraft;
}
