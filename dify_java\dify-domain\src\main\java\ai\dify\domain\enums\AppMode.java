package ai.dify.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应用模式枚举
 */
@Getter
@AllArgsConstructor
public enum AppMode {

    /**
     * 聊天助手
     */
    CHAT("chat", "聊天助手"),

    /**
     * 文本生成
     */
    COMPLETION("completion", "文本生成"),

    /**
     * 工作流
     */
    WORKFLOW("workflow", "工作流"),

    /**
     * Agent
     */
    AGENT("agent", "智能体");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static AppMode fromCode(String code) {
        for (AppMode mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("Unknown app mode: " + code);
    }
}
