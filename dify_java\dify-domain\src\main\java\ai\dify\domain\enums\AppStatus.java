package ai.dify.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应用状态枚举
 */
@Getter
@AllArgsConstructor
public enum AppStatus {

    /**
     * 草稿
     */
    DRAFT("draft", "草稿"),

    /**
     * 已发布
     */
    PUBLISHED("published", "已发布"),

    /**
     * 已禁用
     */
    DISABLED("disabled", "已禁用"),

    /**
     * 已删除
     */
    DELETED("deleted", "已删除");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static AppStatus fromCode(String code) {
        for (AppStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown app status: " + code);
    }
}
