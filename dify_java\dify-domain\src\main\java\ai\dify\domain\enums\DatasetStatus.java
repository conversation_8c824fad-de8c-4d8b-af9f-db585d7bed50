package ai.dify.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据集状态枚举
 */
@Getter
@AllArgsConstructor
public enum DatasetStatus {

    /**
     * 可用
     */
    AVAILABLE("available", "可用"),

    /**
     * 处理中
     */
    PROCESSING("processing", "处理中"),

    /**
     * 错误
     */
    ERROR("error", "错误"),

    /**
     * 已禁用
     */
    DISABLED("disabled", "已禁用"),

    /**
     * 已删除
     */
    DELETED("deleted", "已删除");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static DatasetStatus fromCode(String code) {
        for (DatasetStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown dataset status: " + code);
    }
}
