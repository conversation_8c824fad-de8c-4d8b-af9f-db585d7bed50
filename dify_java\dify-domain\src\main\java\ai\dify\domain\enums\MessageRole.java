package ai.dify.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息角色枚举
 */
@Getter
@AllArgsConstructor
public enum MessageRole {

    /**
     * 用户
     */
    USER("user", "用户"),

    /**
     * 助手
     */
    ASSISTANT("assistant", "助手"),

    /**
     * 系统
     */
    SYSTEM("system", "系统"),

    /**
     * 工具
     */
    TOOL("tool", "工具");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static MessageRole fromCode(String code) {
        for (MessageRole role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        throw new IllegalArgumentException("Unknown message role: " + code);
    }
}
