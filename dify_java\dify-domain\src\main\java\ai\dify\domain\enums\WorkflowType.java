package ai.dify.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作流类型枚举
 */
@Getter
@AllArgsConstructor
public enum WorkflowType {

    /**
     * 聊天流
     */
    CHAT("chat", "聊天流"),

    /**
     * 工作流
     */
    WORKFLOW("workflow", "工作流");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static WorkflowType fromCode(String code) {
        for (WorkflowType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown workflow type: " + code);
    }
}
