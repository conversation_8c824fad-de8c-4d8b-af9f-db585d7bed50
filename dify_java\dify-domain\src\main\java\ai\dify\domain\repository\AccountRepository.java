package ai.dify.domain.repository;

import ai.dify.domain.entity.Account;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 账户数据访问接口
 */
@Mapper
public interface AccountRepository extends BaseMapper<Account> {

    /**
     * 根据邮箱查询账户
     */
    @Select("SELECT * FROM accounts WHERE email = #{email} LIMIT 1")
    Account findByEmail(@Param("email") String email);

    /**
     * 根据邮箱和状态查询账户
     */
    @Select("SELECT * FROM accounts WHERE email = #{email} AND status = #{status} LIMIT 1")
    Account findByEmailAndStatus(@Param("email") String email, @Param("status") String status);

    /**
     * 根据状态查询账户列表
     */
    @Select("SELECT * FROM accounts WHERE status = #{status} ORDER BY created_at DESC")
    List<Account> findByStatus(@Param("status") String status);

    /**
     * 更新最后登录信息
     */
    @Update("UPDATE accounts SET last_login_at = #{lastLoginAt}, last_login_ip = #{lastLoginIp}, last_active_at = #{lastActiveAt} WHERE id = #{id}")
    int updateLastLoginInfo(@Param("id") String id, 
                           @Param("lastLoginAt") LocalDateTime lastLoginAt, 
                           @Param("lastLoginIp") String lastLoginIp,
                           @Param("lastActiveAt") LocalDateTime lastActiveAt);

    /**
     * 更新最后活跃时间
     */
    @Update("UPDATE accounts SET last_active_at = #{lastActiveAt} WHERE id = #{id}")
    int updateLastActiveAt(@Param("id") String id, @Param("lastActiveAt") LocalDateTime lastActiveAt);

    /**
     * 更新密码
     */
    @Update("UPDATE accounts SET password_hash = #{passwordHash}, password_salt = #{passwordSalt} WHERE id = #{id}")
    int updatePassword(@Param("id") String id, 
                      @Param("passwordHash") String passwordHash, 
                      @Param("passwordSalt") String passwordSalt);

    /**
     * 更新账户状态
     */
    @Update("UPDATE accounts SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") String status);

    /**
     * 更新初始化状态
     */
    @Update("UPDATE accounts SET initialized = #{initialized} WHERE id = #{id}")
    int updateInitialized(@Param("id") String id, @Param("initialized") Boolean initialized);

    /**
     * 统计账户总数
     */
    @Select("SELECT COUNT(*) FROM accounts")
    Long countAll();

    /**
     * 根据状态统计账户数量
     */
    @Select("SELECT COUNT(*) FROM accounts WHERE status = #{status}")
    Long countByStatus(@Param("status") String status);

    /**
     * 查询最近注册的账户
     */
    @Select("SELECT * FROM accounts ORDER BY created_at DESC LIMIT #{limit}")
    List<Account> findRecentAccounts(@Param("limit") Integer limit);

    /**
     * 查询最近活跃的账户
     */
    @Select("SELECT * FROM accounts WHERE last_active_at IS NOT NULL ORDER BY last_active_at DESC LIMIT #{limit}")
    List<Account> findRecentActiveAccounts(@Param("limit") Integer limit);

    /**
     * 根据时间范围查询注册的账户
     */
    @Select("SELECT * FROM accounts WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<Account> findByCreatedAtRange(@Param("startTime") LocalDateTime startTime, 
                                      @Param("endTime") LocalDateTime endTime);
}
