package ai.dify.domain.repository;

import ai.dify.domain.entity.App;
import ai.dify.domain.enums.AppMode;
import ai.dify.domain.enums.AppStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 应用数据访问接口
 */
@Mapper
public interface AppRepository extends BaseMapper<App> {

    /**
     * 根据租户ID分页查询应用
     */
    @Select("SELECT * FROM apps WHERE tenant_id = #{tenantId} AND status != 'deleted' ORDER BY created_at DESC")
    IPage<App> findByTenantId(Page<App> page, @Param("tenantId") String tenantId);

    /**
     * 根据租户ID和状态查询应用
     */
    @Select("SELECT * FROM apps WHERE tenant_id = #{tenantId} AND status = #{status} ORDER BY created_at DESC")
    List<App> findByTenantIdAndStatus(@Param("tenantId") String tenantId, @Param("status") AppStatus status);

    /**
     * 根据租户ID和模式查询应用
     */
    @Select("SELECT * FROM apps WHERE tenant_id = #{tenantId} AND mode = #{mode} AND status != 'deleted' ORDER BY created_at DESC")
    List<App> findByTenantIdAndMode(@Param("tenantId") String tenantId, @Param("mode") AppMode mode);

    /**
     * 根据租户ID和名称查询应用
     */
    @Select("SELECT * FROM apps WHERE tenant_id = #{tenantId} AND name = #{name} AND status != 'deleted' LIMIT 1")
    App findByTenantIdAndName(@Param("tenantId") String tenantId, @Param("name") String name);

    /**
     * 根据租户ID和关键词搜索应用
     */
    @Select("SELECT * FROM apps WHERE tenant_id = #{tenantId} AND (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) AND status != 'deleted' ORDER BY created_at DESC")
    IPage<App> searchByKeyword(Page<App> page, @Param("tenantId") String tenantId, @Param("keyword") String keyword);

    /**
     * 统计租户下的应用数量
     */
    @Select("SELECT COUNT(*) FROM apps WHERE tenant_id = #{tenantId} AND status != 'deleted'")
    Long countByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据租户ID和模式统计应用数量
     */
    @Select("SELECT COUNT(*) FROM apps WHERE tenant_id = #{tenantId} AND mode = #{mode} AND status != 'deleted'")
    Long countByTenantIdAndMode(@Param("tenantId") String tenantId, @Param("mode") AppMode mode);

    /**
     * 查询公开的模板应用
     */
    @Select("SELECT * FROM apps WHERE is_public = true AND is_template = true AND status = 'published' ORDER BY created_at DESC")
    IPage<App> findPublicTemplates(Page<App> page);

    /**
     * 根据创建者查询应用
     */
    @Select("SELECT * FROM apps WHERE created_by = #{createdBy} AND status != 'deleted' ORDER BY created_at DESC")
    List<App> findByCreatedBy(@Param("createdBy") String createdBy);
}
