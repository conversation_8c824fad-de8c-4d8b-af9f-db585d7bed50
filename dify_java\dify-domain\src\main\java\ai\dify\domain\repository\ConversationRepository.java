package ai.dify.domain.repository;

import ai.dify.domain.entity.Conversation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 对话数据访问接口
 */
@Mapper
public interface ConversationRepository extends BaseMapper<Conversation> {

    /**
     * 根据应用ID分页查询对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} ORDER BY created_at DESC")
    IPage<Conversation> findByAppId(Page<Conversation> page, @Param("appId") String appId);

    /**
     * 根据应用ID和终端用户ID查询对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} AND from_end_user_id = #{endUserId} ORDER BY created_at DESC")
    IPage<Conversation> findByAppIdAndEndUserId(Page<Conversation> page, 
                                               @Param("appId") String appId, 
                                               @Param("endUserId") String endUserId);

    /**
     * 根据应用ID和账户ID查询对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} AND from_account_id = #{accountId} ORDER BY created_at DESC")
    IPage<Conversation> findByAppIdAndAccountId(Page<Conversation> page, 
                                               @Param("appId") String appId, 
                                               @Param("accountId") String accountId);

    /**
     * 根据终端用户ID查询对话
     */
    @Select("SELECT * FROM conversations WHERE from_end_user_id = #{endUserId} ORDER BY created_at DESC")
    List<Conversation> findByEndUserId(@Param("endUserId") String endUserId);

    /**
     * 根据应用ID和状态查询对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} AND status = #{status} ORDER BY created_at DESC")
    List<Conversation> findByAppIdAndStatus(@Param("appId") String appId, @Param("status") String status);

    /**
     * 根据应用ID查询置顶对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} AND is_pinned = true ORDER BY created_at DESC")
    List<Conversation> findPinnedByAppId(@Param("appId") String appId);

    /**
     * 根据应用ID和时间范围查询对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} AND created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<Conversation> findByAppIdAndTimeRange(@Param("appId") String appId, 
                                              @Param("startTime") LocalDateTime startTime, 
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 统计应用下的对话数量
     */
    @Select("SELECT COUNT(*) FROM conversations WHERE app_id = #{appId}")
    Long countByAppId(@Param("appId") String appId);

    /**
     * 统计终端用户的对话数量
     */
    @Select("SELECT COUNT(*) FROM conversations WHERE from_end_user_id = #{endUserId}")
    Long countByEndUserId(@Param("endUserId") String endUserId);

    /**
     * 根据应用ID和日期统计对话数量
     */
    @Select("SELECT COUNT(*) FROM conversations WHERE app_id = #{appId} AND DATE(created_at) = #{date}")
    Long countByAppIdAndDate(@Param("appId") String appId, @Param("date") String date);

    /**
     * 查询最近活跃的对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} ORDER BY updated_at DESC LIMIT #{limit}")
    List<Conversation> findRecentActiveByAppId(@Param("appId") String appId, @Param("limit") Integer limit);

    /**
     * 根据名称搜索对话
     */
    @Select("SELECT * FROM conversations WHERE app_id = #{appId} AND name LIKE CONCAT('%', #{keyword}, '%') ORDER BY created_at DESC")
    IPage<Conversation> searchByName(Page<Conversation> page, @Param("appId") String appId, @Param("keyword") String keyword);
}
