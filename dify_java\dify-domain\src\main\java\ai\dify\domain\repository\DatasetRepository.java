package ai.dify.domain.repository;

import ai.dify.domain.entity.Dataset;
import ai.dify.domain.enums.DatasetStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据集数据访问接口
 */
@Mapper
public interface DatasetRepository extends BaseMapper<Dataset> {

    /**
     * 根据租户ID分页查询数据集
     */
    @Select("SELECT * FROM datasets WHERE tenant_id = #{tenantId} AND status != 'deleted' ORDER BY created_at DESC")
    IPage<Dataset> findByTenantId(Page<Dataset> page, @Param("tenantId") String tenantId);

    /**
     * 根据租户ID和状态查询数据集
     */
    @Select("SELECT * FROM datasets WHERE tenant_id = #{tenantId} AND status = #{status} ORDER BY created_at DESC")
    List<Dataset> findByTenantIdAndStatus(@Param("tenantId") String tenantId, @Param("status") DatasetStatus status);

    /**
     * 根据租户ID和名称查询数据集
     */
    @Select("SELECT * FROM datasets WHERE tenant_id = #{tenantId} AND name = #{name} AND status != 'deleted' LIMIT 1")
    Dataset findByTenantIdAndName(@Param("tenantId") String tenantId, @Param("name") String name);

    /**
     * 根据租户ID和关键词搜索数据集
     */
    @Select("SELECT * FROM datasets WHERE tenant_id = #{tenantId} AND (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) AND status != 'deleted' ORDER BY created_at DESC")
    IPage<Dataset> searchByKeyword(Page<Dataset> page, @Param("tenantId") String tenantId, @Param("keyword") String keyword);

    /**
     * 统计租户下的数据集数量
     */
    @Select("SELECT COUNT(*) FROM datasets WHERE tenant_id = #{tenantId} AND status != 'deleted'")
    Long countByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据向量存储类型查询数据集
     */
    @Select("SELECT * FROM datasets WHERE vector_store = #{vectorStore} AND status = 'available' ORDER BY created_at DESC")
    List<Dataset> findByVectorStore(@Param("vectorStore") String vectorStore);

    /**
     * 根据嵌入模型查询数据集
     */
    @Select("SELECT * FROM datasets WHERE embedding_model = #{embeddingModel} AND status = 'available' ORDER BY created_at DESC")
    List<Dataset> findByEmbeddingModel(@Param("embeddingModel") String embeddingModel);

    /**
     * 根据创建者查询数据集
     */
    @Select("SELECT * FROM datasets WHERE created_by = #{createdBy} AND status != 'deleted' ORDER BY created_at DESC")
    List<Dataset> findByCreatedBy(@Param("createdBy") String createdBy);

    /**
     * 查询处理中的数据集
     */
    @Select("SELECT * FROM datasets WHERE status = 'processing' ORDER BY created_at ASC")
    List<Dataset> findProcessingDatasets();
}
