package ai.dify.domain.repository;

import ai.dify.domain.entity.Message;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 消息数据访问接口
 */
@Mapper
public interface MessageRepository extends BaseMapper<Message> {

    /**
     * 根据对话ID分页查询消息
     */
    @Select("SELECT * FROM messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC")
    IPage<Message> findByConversationId(Page<Message> page, @Param("conversationId") String conversationId);

    /**
     * 根据对话ID查询消息列表
     */
    @Select("SELECT * FROM messages WHERE conversation_id = #{conversationId} ORDER BY created_at ASC")
    List<Message> findByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据应用ID分页查询消息
     */
    @Select("SELECT * FROM messages WHERE app_id = #{appId} ORDER BY created_at DESC")
    IPage<Message> findByAppId(Page<Message> page, @Param("appId") String appId);

    /**
     * 根据应用ID和时间范围查询消息
     */
    @Select("SELECT * FROM messages WHERE app_id = #{appId} AND created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<Message> findByAppIdAndTimeRange(@Param("appId") String appId, 
                                         @Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 根据终端用户ID查询消息
     */
    @Select("SELECT * FROM messages WHERE from_end_user_id = #{endUserId} ORDER BY created_at DESC")
    IPage<Message> findByEndUserId(Page<Message> page, @Param("endUserId") String endUserId);

    /**
     * 根据工作流运行ID查询消息
     */
    @Select("SELECT * FROM messages WHERE workflow_run_id = #{workflowRunId} ORDER BY created_at ASC")
    List<Message> findByWorkflowRunId(@Param("workflowRunId") String workflowRunId);

    /**
     * 根据父消息ID查询子消息
     */
    @Select("SELECT * FROM messages WHERE parent_message_id = #{parentMessageId} ORDER BY created_at ASC")
    List<Message> findByParentMessageId(@Param("parentMessageId") String parentMessageId);

    /**
     * 统计应用下的消息数量
     */
    @Select("SELECT COUNT(*) FROM messages WHERE app_id = #{appId}")
    Long countByAppId(@Param("appId") String appId);

    /**
     * 统计对话下的消息数量
     */
    @Select("SELECT COUNT(*) FROM messages WHERE conversation_id = #{conversationId}")
    Long countByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据状态查询消息
     */
    @Select("SELECT * FROM messages WHERE status = #{status} ORDER BY created_at DESC")
    List<Message> findByStatus(@Param("status") String status);

    /**
     * 查询最近的消息
     */
    @Select("SELECT * FROM messages WHERE conversation_id = #{conversationId} ORDER BY created_at DESC LIMIT 1")
    Message findLatestByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据应用ID和日期统计消息数量
     */
    @Select("SELECT COUNT(*) FROM messages WHERE app_id = #{appId} AND DATE(created_at) = #{date}")
    Long countByAppIdAndDate(@Param("appId") String appId, @Param("date") String date);
}
