package ai.dify.domain.repository;

import ai.dify.domain.entity.Workflow;
import ai.dify.domain.enums.WorkflowType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 工作流数据访问接口
 */
@Mapper
public interface WorkflowRepository extends BaseMapper<Workflow> {

    /**
     * 根据应用ID查询工作流
     */
    @Select("SELECT * FROM workflows WHERE app_id = #{appId} ORDER BY created_at DESC")
    List<Workflow> findByAppId(@Param("appId") String appId);

    /**
     * 根据应用ID和类型查询工作流
     */
    @Select("SELECT * FROM workflows WHERE app_id = #{appId} AND type = #{type} ORDER BY created_at DESC")
    List<Workflow> findByAppIdAndType(@Param("appId") String appId, @Param("type") WorkflowType type);

    /**
     * 根据应用ID查询已发布的工作流
     */
    @Select("SELECT * FROM workflows WHERE app_id = #{appId} AND is_published = true ORDER BY created_at DESC LIMIT 1")
    Workflow findPublishedByAppId(@Param("appId") String appId);

    /**
     * 根据应用ID查询草稿工作流
     */
    @Select("SELECT * FROM workflows WHERE app_id = #{appId} AND is_draft = true ORDER BY created_at DESC LIMIT 1")
    Workflow findDraftByAppId(@Param("appId") String appId);

    /**
     * 根据租户ID查询工作流
     */
    @Select("SELECT * FROM workflows WHERE tenant_id = #{tenantId} ORDER BY created_at DESC")
    List<Workflow> findByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据租户ID和类型查询工作流
     */
    @Select("SELECT * FROM workflows WHERE tenant_id = #{tenantId} AND type = #{type} ORDER BY created_at DESC")
    List<Workflow> findByTenantIdAndType(@Param("tenantId") String tenantId, @Param("type") WorkflowType type);

    /**
     * 统计应用下的工作流数量
     */
    @Select("SELECT COUNT(*) FROM workflows WHERE app_id = #{appId}")
    Long countByAppId(@Param("appId") String appId);

    /**
     * 根据创建者查询工作流
     */
    @Select("SELECT * FROM workflows WHERE created_by = #{createdBy} ORDER BY created_at DESC")
    List<Workflow> findByCreatedBy(@Param("createdBy") String createdBy);
}
