package ai.dify.model.runtime;

import ai.dify.model.runtime.model.ModelConfig;
import ai.dify.model.runtime.model.ModelResponse;
import ai.dify.model.runtime.model.ModelRequest;
import ai.dify.model.runtime.provider.ModelProvider;
import ai.dify.model.runtime.provider.ProviderManager;
import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 模型运行时 - 统一的模型调用接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ModelRuntime {

    private final ProviderManager providerManager;

    /**
     * 调用语言模型（同步）
     */
    public ModelResponse invoke(ModelRequest request) {
        log.debug("调用模型: provider={}, model={}", request.getProvider(), request.getModel());
        
        try {
            // 获取模型提供商
            ModelProvider provider = providerManager.getProvider(request.getProvider());
            if (provider == null) {
                throw new DifyException(ResultCode.MODEL_NOT_FOUND, 
                    "不支持的模型提供商: " + request.getProvider());
            }

            // 验证模型配置
            validateModelConfig(request);

            // 调用模型
            ModelResponse response = provider.invoke(request);
            
            log.debug("模型调用完成: provider={}, model={}, tokens={}", 
                request.getProvider(), request.getModel(), response.getUsage().getTotalTokens());
            
            return response;
            
        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("模型调用失败: provider={}, model={}", request.getProvider(), request.getModel(), e);
            throw new DifyException(ResultCode.MODEL_REQUEST_ERROR, "模型调用失败: " + e.getMessage());
        }
    }

    /**
     * 调用语言模型（异步）
     */
    public Mono<ModelResponse> invokeAsync(ModelRequest request) {
        return Mono.fromCallable(() -> invoke(request));
    }

    /**
     * 流式调用语言模型
     */
    public Flux<ModelResponse> invokeStream(ModelRequest request) {
        log.debug("流式调用模型: provider={}, model={}", request.getProvider(), request.getModel());
        
        try {
            // 获取模型提供商
            ModelProvider provider = providerManager.getProvider(request.getProvider());
            if (provider == null) {
                return Flux.error(new DifyException(ResultCode.MODEL_NOT_FOUND, 
                    "不支持的模型提供商: " + request.getProvider()));
            }

            // 验证模型配置
            validateModelConfig(request);

            // 流式调用模型
            return provider.invokeStream(request);
            
        } catch (Exception e) {
            log.error("流式模型调用失败: provider={}, model={}", request.getProvider(), request.getModel(), e);
            return Flux.error(new DifyException(ResultCode.MODEL_REQUEST_ERROR, "流式模型调用失败: " + e.getMessage()));
        }
    }

    /**
     * 获取支持的模型列表
     */
    public List<ModelConfig> getSupportedModels(String provider) {
        ModelProvider modelProvider = providerManager.getProvider(provider);
        if (modelProvider == null) {
            throw new DifyException(ResultCode.MODEL_NOT_FOUND, "不支持的模型提供商: " + provider);
        }
        
        return modelProvider.getSupportedModels();
    }

    /**
     * 获取所有支持的提供商
     */
    public Map<String, List<ModelConfig>> getAllSupportedModels() {
        return providerManager.getAllSupportedModels();
    }

    /**
     * 验证模型是否可用
     */
    public boolean validateModel(String provider, String model) {
        try {
            ModelProvider modelProvider = providerManager.getProvider(provider);
            if (modelProvider == null) {
                return false;
            }
            
            return modelProvider.isModelSupported(model);
        } catch (Exception e) {
            log.warn("验证模型失败: provider={}, model={}", provider, model, e);
            return false;
        }
    }

    /**
     * 计算Token数量
     */
    public int calculateTokens(String provider, String model, String text) {
        try {
            ModelProvider modelProvider = providerManager.getProvider(provider);
            if (modelProvider == null) {
                // 使用默认估算方法
                return estimateTokens(text);
            }
            
            return modelProvider.calculateTokens(model, text);
        } catch (Exception e) {
            log.warn("计算Token失败: provider={}, model={}", provider, model, e);
            return estimateTokens(text);
        }
    }

    /**
     * 获取模型配置
     */
    public ModelConfig getModelConfig(String provider, String model) {
        ModelProvider modelProvider = providerManager.getProvider(provider);
        if (modelProvider == null) {
            throw new DifyException(ResultCode.MODEL_NOT_FOUND, "不支持的模型提供商: " + provider);
        }
        
        return modelProvider.getModelConfig(model);
    }

    /**
     * 验证模型配置
     */
    private void validateModelConfig(ModelRequest request) {
        if (request.getProvider() == null || request.getProvider().trim().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "模型提供商不能为空");
        }
        
        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "模型名称不能为空");
        }
        
        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "消息列表不能为空");
        }
        
        // 验证温度参数
        if (request.getTemperature() != null && 
            (request.getTemperature() < 0 || request.getTemperature() > 2)) {
            throw new DifyException(ResultCode.PARAM_ERROR, "温度参数必须在0-2之间");
        }
        
        // 验证最大Token数
        if (request.getMaxTokens() != null && request.getMaxTokens() <= 0) {
            throw new DifyException(ResultCode.PARAM_ERROR, "最大Token数必须大于0");
        }
    }

    /**
     * 估算Token数量（简单实现）
     */
    private int estimateTokens(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        
        // 简单估算：英文约4个字符=1个token，中文约1.5个字符=1个token
        int chineseChars = 0;
        int otherChars = 0;
        
        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                chineseChars++;
            } else {
                otherChars++;
            }
        }
        
        return (int) Math.ceil(chineseChars / 1.5) + (int) Math.ceil(otherChars / 4.0);
    }
}
