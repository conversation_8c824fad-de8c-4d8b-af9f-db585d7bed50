package ai.dify.model.runtime.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天消息
 */
@Data
public class ChatMessage {

    /**
     * 消息角色
     */
    private String role;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息名称（可选）
     */
    private String name;

    /**
     * 工具调用（assistant消息时使用）
     */
    private List<ToolCall> toolCalls;

    /**
     * 工具调用ID（tool消息时使用）
     */
    private String toolCallId;

    /**
     * 额外属性
     */
    private Map<String, Object> extraProperties;

    /**
     * 创建用户消息
     */
    public static ChatMessage user(String content) {
        ChatMessage message = new ChatMessage();
        message.setRole("user");
        message.setContent(content);
        return message;
    }

    /**
     * 创建助手消息
     */
    public static ChatMessage assistant(String content) {
        ChatMessage message = new ChatMessage();
        message.setRole("assistant");
        message.setContent(content);
        return message;
    }

    /**
     * 创建系统消息
     */
    public static ChatMessage system(String content) {
        ChatMessage message = new ChatMessage();
        message.setRole("system");
        message.setContent(content);
        return message;
    }

    /**
     * 创建工具消息
     */
    public static ChatMessage tool(String content, String toolCallId) {
        ChatMessage message = new ChatMessage();
        message.setRole("tool");
        message.setContent(content);
        message.setToolCallId(toolCallId);
        return message;
    }

    /**
     * 创建带工具调用的助手消息
     */
    public static ChatMessage assistantWithToolCalls(List<ToolCall> toolCalls) {
        ChatMessage message = new ChatMessage();
        message.setRole("assistant");
        message.setToolCalls(toolCalls);
        return message;
    }

    /**
     * 设置消息名称
     */
    public ChatMessage withName(String name) {
        this.name = name;
        return this;
    }

    /**
     * 添加额外属性
     */
    public ChatMessage withExtraProperty(String key, Object value) {
        if (this.extraProperties == null) {
            this.extraProperties = new java.util.HashMap<>();
        }
        this.extraProperties.put(key, value);
        return this;
    }

    /**
     * 工具调用
     */
    @Data
    public static class ToolCall {
        /**
         * 调用ID
         */
        private String id;

        /**
         * 类型（通常是"function"）
         */
        private String type;

        /**
         * 函数调用
         */
        private FunctionCall function;

        /**
         * 函数调用
         */
        @Data
        public static class FunctionCall {
            /**
             * 函数名称
             */
            private String name;

            /**
             * 函数参数（JSON字符串）
             */
            private String arguments;
        }
    }
}
