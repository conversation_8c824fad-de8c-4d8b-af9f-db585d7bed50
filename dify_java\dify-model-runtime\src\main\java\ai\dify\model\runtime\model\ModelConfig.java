package ai.dify.model.runtime.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 模型配置
 */
@Data
public class ModelConfig {

    /**
     * 模型名称
     */
    private String name;

    /**
     * 模型显示名称
     */
    private String displayName;

    /**
     * 模型描述
     */
    private String description;

    /**
     * 模型类型
     */
    private ModelType type;

    /**
     * 模型提供商
     */
    private String provider;

    /**
     * 最大Token数
     */
    private Integer maxTokens;

    /**
     * 上下文长度
     */
    private Integer contextLength;

    /**
     * 支持的功能
     */
    private List<String> capabilities;

    /**
     * 输入价格（每1K token）
     */
    private Double inputPrice;

    /**
     * 输出价格（每1K token）
     */
    private Double outputPrice;

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 是否支持流式输出
     */
    private Boolean supportsStreaming;

    /**
     * 是否支持工具调用
     */
    private Boolean supportsToolCalls;

    /**
     * 是否支持视觉输入
     */
    private Boolean supportsVision;

    /**
     * 是否支持函数调用
     */
    private Boolean supportsFunctionCalling;

    /**
     * 默认参数
     */
    private Map<String, Object> defaultParams;

    /**
     * 参数约束
     */
    private Map<String, ParameterConstraint> parameterConstraints;

    /**
     * 模型标签
     */
    private List<String> tags;

    /**
     * 是否可用
     */
    private Boolean available;

    /**
     * 模型版本
     */
    private String version;

    /**
     * 发布日期
     */
    private String releaseDate;

    /**
     * 模型类型枚举
     */
    public enum ModelType {
        /**
         * 文本生成模型
         */
        TEXT_GENERATION,

        /**
         * 聊天模型
         */
        CHAT,

        /**
         * 嵌入模型
         */
        EMBEDDING,

        /**
         * 重排序模型
         */
        RERANK,

        /**
         * 语音转文本模型
         */
        SPEECH_TO_TEXT,

        /**
         * 文本转语音模型
         */
        TEXT_TO_SPEECH,

        /**
         * 图像生成模型
         */
        IMAGE_GENERATION,

        /**
         * 图像理解模型
         */
        IMAGE_UNDERSTANDING,

        /**
         * 多模态模型
         */
        MULTIMODAL
    }

    /**
     * 参数约束
     */
    @Data
    public static class ParameterConstraint {
        /**
         * 最小值
         */
        private Double min;

        /**
         * 最大值
         */
        private Double max;

        /**
         * 默认值
         */
        private Object defaultValue;

        /**
         * 是否必需
         */
        private Boolean required;

        /**
         * 参数类型
         */
        private String type;

        /**
         * 参数描述
         */
        private String description;

        /**
         * 可选值列表
         */
        private List<Object> options;
    }

    /**
     * 创建基础模型配置
     */
    public static ModelConfig create(String name, String provider, ModelType type) {
        ModelConfig config = new ModelConfig();
        config.setName(name);
        config.setProvider(provider);
        config.setType(type);
        config.setAvailable(true);
        return config;
    }

    /**
     * 设置价格信息
     */
    public ModelConfig withPricing(Double inputPrice, Double outputPrice, String currency) {
        this.inputPrice = inputPrice;
        this.outputPrice = outputPrice;
        this.currency = currency;
        return this;
    }

    /**
     * 设置Token限制
     */
    public ModelConfig withTokenLimits(Integer maxTokens, Integer contextLength) {
        this.maxTokens = maxTokens;
        this.contextLength = contextLength;
        return this;
    }

    /**
     * 设置功能支持
     */
    public ModelConfig withCapabilities(List<String> capabilities) {
        this.capabilities = capabilities;
        return this;
    }

    /**
     * 检查是否支持指定功能
     */
    public boolean supportsCapability(String capability) {
        return capabilities != null && capabilities.contains(capability);
    }

    /**
     * 检查是否为聊天模型
     */
    public boolean isChatModel() {
        return ModelType.CHAT.equals(type) || ModelType.MULTIMODAL.equals(type);
    }

    /**
     * 检查是否为嵌入模型
     */
    public boolean isEmbeddingModel() {
        return ModelType.EMBEDDING.equals(type);
    }

    /**
     * 检查是否为重排序模型
     */
    public boolean isRerankModel() {
        return ModelType.RERANK.equals(type);
    }
}
