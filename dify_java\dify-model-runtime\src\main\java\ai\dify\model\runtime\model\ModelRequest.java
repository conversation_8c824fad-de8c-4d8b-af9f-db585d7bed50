package ai.dify.model.runtime.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 模型请求
 */
@Data
public class ModelRequest {

    /**
     * 模型提供商
     */
    @NotBlank(message = "模型提供商不能为空")
    private String provider;

    /**
     * 模型名称
     */
    @NotBlank(message = "模型名称不能为空")
    private String model;

    /**
     * 消息列表
     */
    @NotEmpty(message = "消息列表不能为空")
    private List<ChatMessage> messages;

    /**
     * 温度参数 (0.0-2.0)
     */
    private Double temperature;

    /**
     * 最大Token数
     */
    private Integer maxTokens;

    /**
     * Top-p参数
     */
    private Double topP;

    /**
     * 频率惩罚
     */
    private Double frequencyPenalty;

    /**
     * 存在惩罚
     */
    private Double presencePenalty;

    /**
     * 停止词
     */
    private List<String> stop;

    /**
     * 是否流式响应
     */
    private Boolean stream = false;

    /**
     * 用户标识
     */
    private String user;

    /**
     * 工具列表
     */
    private List<Tool> tools;

    /**
     * 工具选择策略
     */
    private String toolChoice;

    /**
     * 响应格式
     */
    private ResponseFormat responseFormat;

    /**
     * 种子值（用于可重现的输出）
     */
    private Integer seed;

    /**
     * 额外参数
     */
    private Map<String, Object> extraParams;

    /**
     * 创建简单的文本请求
     */
    public static ModelRequest createTextRequest(String provider, String model, String content) {
        ModelRequest request = new ModelRequest();
        request.setProvider(provider);
        request.setModel(model);
        request.setMessages(List.of(ChatMessage.user(content)));
        return request;
    }

    /**
     * 创建对话请求
     */
    public static ModelRequest createChatRequest(String provider, String model, List<ChatMessage> messages) {
        ModelRequest request = new ModelRequest();
        request.setProvider(provider);
        request.setModel(model);
        request.setMessages(messages);
        return request;
    }

    /**
     * 设置温度参数
     */
    public ModelRequest withTemperature(Double temperature) {
        this.temperature = temperature;
        return this;
    }

    /**
     * 设置最大Token数
     */
    public ModelRequest withMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
        return this;
    }

    /**
     * 设置流式响应
     */
    public ModelRequest withStream(Boolean stream) {
        this.stream = stream;
        return this;
    }

    /**
     * 设置用户标识
     */
    public ModelRequest withUser(String user) {
        this.user = user;
        return this;
    }

    /**
     * 添加工具
     */
    public ModelRequest withTools(List<Tool> tools) {
        this.tools = tools;
        return this;
    }

    /**
     * 设置响应格式
     */
    public ModelRequest withResponseFormat(ResponseFormat responseFormat) {
        this.responseFormat = responseFormat;
        return this;
    }
}
