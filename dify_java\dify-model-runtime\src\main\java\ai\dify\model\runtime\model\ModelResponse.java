package ai.dify.model.runtime.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 模型响应
 */
@Data
public class ModelResponse {

    /**
     * 响应ID
     */
    private String id;

    /**
     * 对象类型
     */
    private String object;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 选择列表
     */
    private List<Choice> choices;

    /**
     * 使用情况
     */
    private Usage usage;

    /**
     * 系统指纹
     */
    private String systemFingerprint;

    /**
     * 是否流式响应
     */
    private Boolean stream = false;

    /**
     * 完成原因
     */
    private String finishReason;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 额外数据
     */
    private Map<String, Object> extraData;

    /**
     * 选择项
     */
    @Data
    public static class Choice {
        /**
         * 索引
         */
        private Integer index;

        /**
         * 消息
         */
        private ChatMessage message;

        /**
         * Delta（流式响应时使用）
         */
        private ChatMessage delta;

        /**
         * 完成原因
         */
        private String finishReason;

        /**
         * 日志概率
         */
        private Object logprobs;
    }

    /**
     * 使用情况统计
     */
    @Data
    public static class Usage {
        /**
         * 提示Token数
         */
        private Integer promptTokens;

        /**
         * 完成Token数
         */
        private Integer completionTokens;

        /**
         * 总Token数
         */
        private Integer totalTokens;

        /**
         * 提示Token详情
         */
        private TokenDetails promptTokensDetails;

        /**
         * 完成Token详情
         */
        private TokenDetails completionTokensDetails;
    }

    /**
     * Token详情
     */
    @Data
    public static class TokenDetails {
        /**
         * 缓存Token数
         */
        private Integer cachedTokens;

        /**
         * 音频Token数
         */
        private Integer audioTokens;
    }

    /**
     * 创建成功响应
     */
    public static ModelResponse success(String id, String model, List<Choice> choices, Usage usage) {
        ModelResponse response = new ModelResponse();
        response.setId(id);
        response.setModel(model);
        response.setChoices(choices);
        response.setUsage(usage);
        response.setCreated(LocalDateTime.now());
        response.setObject("chat.completion");
        return response;
    }

    /**
     * 创建错误响应
     */
    public static ModelResponse error(String error) {
        ModelResponse response = new ModelResponse();
        response.setError(error);
        response.setCreated(LocalDateTime.now());
        return response;
    }

    /**
     * 创建流式响应
     */
    public static ModelResponse stream(String id, String model, Choice choice) {
        ModelResponse response = new ModelResponse();
        response.setId(id);
        response.setModel(model);
        response.setChoices(List.of(choice));
        response.setStream(true);
        response.setCreated(LocalDateTime.now());
        response.setObject("chat.completion.chunk");
        return response;
    }

    /**
     * 获取第一个选择的消息内容
     */
    public String getContent() {
        if (choices == null || choices.isEmpty()) {
            return null;
        }
        
        Choice firstChoice = choices.get(0);
        if (firstChoice.getMessage() != null) {
            return firstChoice.getMessage().getContent();
        }
        
        if (firstChoice.getDelta() != null) {
            return firstChoice.getDelta().getContent();
        }
        
        return null;
    }

    /**
     * 获取完成原因
     */
    public String getFinishReason() {
        if (finishReason != null) {
            return finishReason;
        }
        
        if (choices != null && !choices.isEmpty()) {
            return choices.get(0).getFinishReason();
        }
        
        return null;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return error == null;
    }

    /**
     * 是否完成
     */
    public boolean isFinished() {
        String reason = getFinishReason();
        return "stop".equals(reason) || "length".equals(reason) || "tool_calls".equals(reason);
    }
}
