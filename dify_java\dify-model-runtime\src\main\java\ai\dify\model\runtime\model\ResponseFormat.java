package ai.dify.model.runtime.model;

import lombok.Data;

import java.util.Map;

/**
 * 响应格式定义
 */
@Data
public class ResponseFormat {

    /**
     * 格式类型
     */
    private String type;

    /**
     * JSON Schema（当type为"json_schema"时使用）
     */
    private JsonSchema jsonSchema;

    /**
     * JSON Schema定义
     */
    @Data
    public static class JsonSchema {
        /**
         * Schema名称
         */
        private String name;

        /**
         * Schema描述
         */
        private String description;

        /**
         * Schema定义
         */
        private Map<String, Object> schema;

        /**
         * 是否严格模式
         */
        private Boolean strict;
    }

    /**
     * 创建文本格式
     */
    public static ResponseFormat text() {
        ResponseFormat format = new ResponseFormat();
        format.setType("text");
        return format;
    }

    /**
     * 创建JSON对象格式
     */
    public static ResponseFormat jsonObject() {
        ResponseFormat format = new ResponseFormat();
        format.setType("json_object");
        return format;
    }

    /**
     * 创建JSON Schema格式
     */
    public static ResponseFormat jsonSchema(String name, String description, Map<String, Object> schema) {
        ResponseFormat format = new ResponseFormat();
        format.setType("json_schema");
        
        JsonSchema jsonSchema = new JsonSchema();
        jsonSchema.setName(name);
        jsonSchema.setDescription(description);
        jsonSchema.setSchema(schema);
        
        format.setJsonSchema(jsonSchema);
        return format;
    }

    /**
     * 创建严格JSON Schema格式
     */
    public static ResponseFormat strictJsonSchema(String name, String description, Map<String, Object> schema) {
        ResponseFormat format = jsonSchema(name, description, schema);
        format.getJsonSchema().setStrict(true);
        return format;
    }
}
