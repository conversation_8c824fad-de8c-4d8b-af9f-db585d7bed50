package ai.dify.model.runtime.model;

import lombok.Data;

import java.util.Map;

/**
 * 工具定义
 */
@Data
public class Tool {

    /**
     * 工具类型（通常是"function"）
     */
    private String type;

    /**
     * 函数定义
     */
    private Function function;

    /**
     * 函数定义
     */
    @Data
    public static class Function {
        /**
         * 函数名称
         */
        private String name;

        /**
         * 函数描述
         */
        private String description;

        /**
         * 参数定义（JSON Schema）
         */
        private Map<String, Object> parameters;
    }

    /**
     * 创建函数工具
     */
    public static Tool function(String name, String description, Map<String, Object> parameters) {
        Tool tool = new Tool();
        tool.setType("function");
        
        Function function = new Function();
        function.setName(name);
        function.setDescription(description);
        function.setParameters(parameters);
        
        tool.setFunction(function);
        return tool;
    }
}
