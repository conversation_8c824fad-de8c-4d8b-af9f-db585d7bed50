package ai.dify.model.runtime.provider;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.model.runtime.model.ModelConfig;
import ai.dify.model.runtime.model.ModelRequest;
import ai.dify.model.runtime.model.ModelResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 抽象模型提供商基类
 */
@Slf4j
@Data
public abstract class AbstractModelProvider implements ModelProvider {

    protected String name;
    protected String displayName;
    protected String description;
    protected String apiKey;
    protected String baseUrl;
    protected Integer timeout = 30000; // 30秒
    protected Integer maxRetries = 3;
    protected Map<String, Object> extraParams = new ConcurrentHashMap<>();

    // 缓存支持的模型列表
    protected List<ModelConfig> cachedModels;
    protected long lastModelsCacheTime = 0;
    protected static final long MODELS_CACHE_TTL = 5 * 60 * 1000; // 5分钟

    @Override
    public ModelResponse invoke(ModelRequest request) {
        validateRequest(request);
        
        try {
            return doInvoke(request);
        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("模型调用失败: provider={}, model={}", getName(), request.getModel(), e);
            throw new DifyException(ResultCode.MODEL_REQUEST_ERROR, "模型调用失败: " + e.getMessage());
        }
    }

    @Override
    public Flux<ModelResponse> invokeStream(ModelRequest request) {
        validateRequest(request);
        
        try {
            return doInvokeStream(request);
        } catch (DifyException e) {
            return Flux.error(e);
        } catch (Exception e) {
            log.error("流式模型调用失败: provider={}, model={}", getName(), request.getModel(), e);
            return Flux.error(new DifyException(ResultCode.MODEL_REQUEST_ERROR, "流式模型调用失败: " + e.getMessage()));
        }
    }

    @Override
    public List<ModelConfig> getSupportedModels() {
        // 检查缓存
        long currentTime = System.currentTimeMillis();
        if (cachedModels != null && (currentTime - lastModelsCacheTime) < MODELS_CACHE_TTL) {
            return cachedModels;
        }

        try {
            cachedModels = loadSupportedModels();
            lastModelsCacheTime = currentTime;
            return cachedModels;
        } catch (Exception e) {
            log.warn("加载支持的模型列表失败: provider={}", getName(), e);
            return cachedModels != null ? cachedModels : List.of();
        }
    }

    @Override
    public ModelConfig getModelConfig(String model) {
        List<ModelConfig> models = getSupportedModels();
        return models.stream()
                .filter(config -> config.getName().equals(model))
                .findFirst()
                .orElse(null);
    }

    @Override
    public boolean isModelSupported(String model) {
        return getModelConfig(model) != null;
    }

    @Override
    public int calculateTokens(String model, String text) {
        // 默认实现：简单估算
        return estimateTokens(text);
    }

    @Override
    public boolean validateCredentials() {
        try {
            return doValidateCredentials();
        } catch (Exception e) {
            log.warn("验证凭据失败: provider={}", getName(), e);
            return false;
        }
    }

    /**
     * 子类实现具体的模型调用逻辑
     */
    protected abstract ModelResponse doInvoke(ModelRequest request);

    /**
     * 子类实现具体的流式模型调用逻辑
     */
    protected abstract Flux<ModelResponse> doInvokeStream(ModelRequest request);

    /**
     * 子类实现加载支持的模型列表
     */
    protected abstract List<ModelConfig> loadSupportedModels();

    /**
     * 子类实现凭据验证逻辑
     */
    protected abstract boolean doValidateCredentials();

    /**
     * 验证请求参数
     */
    protected void validateRequest(ModelRequest request) {
        if (request == null) {
            throw new DifyException(ResultCode.PARAM_ERROR, "请求不能为空");
        }

        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "模型名称不能为空");
        }

        if (!isModelSupported(request.getModel())) {
            throw new DifyException(ResultCode.MODEL_NOT_FOUND, "不支持的模型: " + request.getModel());
        }

        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "消息列表不能为空");
        }
    }

    /**
     * 估算Token数量
     */
    protected int estimateTokens(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 简单估算：英文约4个字符=1个token，中文约1.5个字符=1个token
        int chineseChars = 0;
        int otherChars = 0;

        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) {
                chineseChars++;
            } else {
                otherChars++;
            }
        }

        return (int) Math.ceil(chineseChars / 1.5) + (int) Math.ceil(otherChars / 4.0);
    }

    /**
     * 清除模型缓存
     */
    protected void clearModelsCache() {
        cachedModels = null;
        lastModelsCacheTime = 0;
    }

    @Override
    public ProviderConfig getConfig() {
        ProviderConfig config = new ProviderConfig();
        config.setApiKey(apiKey);
        config.setBaseUrl(baseUrl);
        config.setTimeout(timeout);
        config.setMaxRetries(maxRetries);
        config.setExtraParams(extraParams);
        return config;
    }
}
