package ai.dify.model.runtime.provider;

import ai.dify.model.runtime.model.ModelConfig;
import ai.dify.model.runtime.model.ModelRequest;
import ai.dify.model.runtime.model.ModelResponse;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 模型提供商接口
 */
public interface ModelProvider {

    /**
     * 获取提供商名称
     */
    String getName();

    /**
     * 获取提供商显示名称
     */
    String getDisplayName();

    /**
     * 获取提供商描述
     */
    String getDescription();

    /**
     * 调用模型（同步）
     */
    ModelResponse invoke(ModelRequest request);

    /**
     * 流式调用模型
     */
    Flux<ModelResponse> invokeStream(ModelRequest request);

    /**
     * 获取支持的模型列表
     */
    List<ModelConfig> getSupportedModels();

    /**
     * 获取模型配置
     */
    ModelConfig getModelConfig(String model);

    /**
     * 检查是否支持指定模型
     */
    boolean isModelSupported(String model);

    /**
     * 计算Token数量
     */
    int calculateTokens(String model, String text);

    /**
     * 验证凭据
     */
    boolean validateCredentials();

    /**
     * 获取API基础URL
     */
    String getBaseUrl();

    /**
     * 设置API密钥
     */
    void setApiKey(String apiKey);

    /**
     * 设置API基础URL
     */
    void setBaseUrl(String baseUrl);

    /**
     * 获取提供商配置
     */
    default ProviderConfig getConfig() {
        return new ProviderConfig();
    }

    /**
     * 提供商配置
     */
    class ProviderConfig {
        private String apiKey;
        private String baseUrl;
        private Integer timeout;
        private Integer maxRetries;
        private java.util.Map<String, Object> extraParams;

        // Getters and Setters
        public String getApiKey() { return apiKey; }
        public void setApiKey(String apiKey) { this.apiKey = apiKey; }

        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

        public Integer getTimeout() { return timeout; }
        public void setTimeout(Integer timeout) { this.timeout = timeout; }

        public Integer getMaxRetries() { return maxRetries; }
        public void setMaxRetries(Integer maxRetries) { this.maxRetries = maxRetries; }

        public java.util.Map<String, Object> getExtraParams() { return extraParams; }
        public void setExtraParams(java.util.Map<String, Object> extraParams) { this.extraParams = extraParams; }
    }
}
