package ai.dify.model.runtime.provider;

import ai.dify.model.runtime.model.ModelConfig;
import ai.dify.model.runtime.provider.impl.OpenAIProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 提供商管理器
 */
@Slf4j
@Component
public class ProviderManager {

    private final Map<String, ModelProvider> providers = new ConcurrentHashMap<>();

    public ProviderManager() {
        // 注册内置提供商
        registerProvider("openai", new OpenAIProvider());
        // TODO: 注册其他提供商
        // registerProvider("anthropic", new AnthropicProvider());
        // registerProvider("azure", new AzureOpenAIProvider());
        // registerProvider("local", new LocalModelProvider());
    }

    /**
     * 注册提供商
     */
    public void registerProvider(String name, ModelProvider provider) {
        providers.put(name, provider);
        log.info("注册模型提供商: {}", name);
    }

    /**
     * 获取提供商
     */
    public ModelProvider getProvider(String name) {
        return providers.get(name);
    }

    /**
     * 获取所有提供商名称
     */
    public java.util.Set<String> getProviderNames() {
        return providers.keySet();
    }

    /**
     * 获取所有支持的模型
     */
    public Map<String, List<ModelConfig>> getAllSupportedModels() {
        Map<String, List<ModelConfig>> allModels = new HashMap<>();
        
        for (Map.Entry<String, ModelProvider> entry : providers.entrySet()) {
            String providerName = entry.getKey();
            ModelProvider provider = entry.getValue();
            
            try {
                List<ModelConfig> models = provider.getSupportedModels();
                allModels.put(providerName, models);
            } catch (Exception e) {
                log.warn("获取提供商模型列表失败: {}", providerName, e);
            }
        }
        
        return allModels;
    }

    /**
     * 检查提供商是否存在
     */
    public boolean hasProvider(String name) {
        return providers.containsKey(name);
    }

    /**
     * 移除提供商
     */
    public void removeProvider(String name) {
        ModelProvider removed = providers.remove(name);
        if (removed != null) {
            log.info("移除模型提供商: {}", name);
        }
    }

    /**
     * 获取提供商数量
     */
    public int getProviderCount() {
        return providers.size();
    }

    /**
     * 验证所有提供商
     */
    public Map<String, Boolean> validateAllProviders() {
        Map<String, Boolean> results = new HashMap<>();
        
        for (Map.Entry<String, ModelProvider> entry : providers.entrySet()) {
            String providerName = entry.getKey();
            ModelProvider provider = entry.getValue();
            
            try {
                boolean isValid = provider.validateCredentials();
                results.put(providerName, isValid);
            } catch (Exception e) {
                log.warn("验证提供商失败: {}", providerName, e);
                results.put(providerName, false);
            }
        }
        
        return results;
    }
}
