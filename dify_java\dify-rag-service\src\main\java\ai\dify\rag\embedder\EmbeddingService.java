package ai.dify.rag.embedder;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.model.runtime.ModelRuntime;
import ai.dify.model.runtime.model.ModelRequest;
import ai.dify.model.runtime.model.ModelResponse;
import ai.dify.model.runtime.model.ChatMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 嵌入服务 - 负责文本向量化
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmbeddingService {

    private final ModelRuntime modelRuntime;

    @Value("${dify.embedding.provider:openai}")
    private String defaultProvider;

    @Value("${dify.embedding.model:text-embedding-3-small}")
    private String defaultModel;

    @Value("${dify.embedding.dimension:1536}")
    private int defaultDimension;

    @Value("${dify.embedding.batch-size:100}")
    private int batchSize;

    // 嵌入缓存
    private final ConcurrentMap<String, float[]> embeddingCache = new ConcurrentHashMap<>();

    /**
     * 生成文本嵌入向量
     */
    public float[] embed(String text) {
        return embed(text, defaultProvider, defaultModel);
    }

    /**
     * 生成文本嵌入向量（指定模型）
     */
    public float[] embed(String text, String provider, String model) {
        if (text == null || text.trim().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "文本内容不能为空");
        }

        // 检查缓存
        String cacheKey = generateCacheKey(text, provider, model);
        float[] cachedEmbedding = embeddingCache.get(cacheKey);
        if (cachedEmbedding != null) {
            log.debug("使用缓存的嵌入向量: length={}", cachedEmbedding.length);
            return cachedEmbedding;
        }

        try {
            log.debug("生成嵌入向量: provider={}, model={}, textLength={}", provider, model, text.length());

            // 创建嵌入请求
            ModelRequest request = createEmbeddingRequest(text, provider, model);

            // 调用模型
            ModelResponse response = modelRuntime.invoke(request);

            if (!response.isSuccess()) {
                throw new DifyException(ResultCode.EMBEDDING_ERROR, "嵌入生成失败: " + response.getError());
            }

            // 提取嵌入向量
            float[] embedding = extractEmbedding(response);

            // 缓存结果
            embeddingCache.put(cacheKey, embedding);

            log.debug("嵌入向量生成成功: dimension={}", embedding.length);
            return embedding;

        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成嵌入向量失败: provider={}, model={}", provider, model, e);
            throw new DifyException(ResultCode.EMBEDDING_ERROR, "生成嵌入向量失败: " + e.getMessage());
        }
    }

    /**
     * 批量生成嵌入向量
     */
    public List<float[]> embedBatch(List<String> texts) {
        return embedBatch(texts, defaultProvider, defaultModel);
    }

    /**
     * 批量生成嵌入向量（指定模型）
     */
    public List<float[]> embedBatch(List<String> texts, String provider, String model) {
        if (texts == null || texts.isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "文本列表不能为空");
        }

        log.debug("批量生成嵌入向量: provider={}, model={}, count={}", provider, model, texts.size());

        // 分批处理
        List<float[]> allEmbeddings = new java.util.ArrayList<>();
        
        for (int i = 0; i < texts.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, texts.size());
            List<String> batch = texts.subList(i, endIndex);
            
            List<float[]> batchEmbeddings = processBatch(batch, provider, model);
            allEmbeddings.addAll(batchEmbeddings);
        }

        log.debug("批量嵌入向量生成完成: total={}", allEmbeddings.size());
        return allEmbeddings;
    }

    /**
     * 处理单个批次
     */
    private List<float[]> processBatch(List<String> texts, String provider, String model) {
        List<float[]> embeddings = new java.util.ArrayList<>();
        
        for (String text : texts) {
            try {
                float[] embedding = embed(text, provider, model);
                embeddings.add(embedding);
            } catch (Exception e) {
                log.warn("生成嵌入向量失败，跳过: text={}", text.substring(0, Math.min(50, text.length())), e);
                // 添加零向量作为占位符
                embeddings.add(new float[defaultDimension]);
            }
        }
        
        return embeddings;
    }

    /**
     * 创建嵌入请求
     */
    private ModelRequest createEmbeddingRequest(String text, String provider, String model) {
        // 对于嵌入模型，通常直接传入文本
        // 这里简化处理，实际可能需要根据不同提供商调整
        ModelRequest request = new ModelRequest();
        request.setProvider(provider);
        request.setModel(model);
        request.setMessages(List.of(ChatMessage.user(text)));
        return request;
    }

    /**
     * 提取嵌入向量
     */
    private float[] extractEmbedding(ModelResponse response) {
        // TODO: 根据实际的响应格式提取嵌入向量
        // 这里是模拟实现
        
        // 生成随机向量作为示例
        float[] embedding = new float[defaultDimension];
        java.util.Random random = new java.util.Random();
        for (int i = 0; i < embedding.length; i++) {
            embedding[i] = (float) (random.nextGaussian() * 0.1);
        }
        
        // 归一化向量
        normalizeVector(embedding);
        
        return embedding;
    }

    /**
     * 向量归一化
     */
    private void normalizeVector(float[] vector) {
        double norm = 0.0;
        for (float v : vector) {
            norm += v * v;
        }
        norm = Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] = (float) (vector[i] / norm);
            }
        }
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String text, String provider, String model) {
        return String.format("%s:%s:%d", provider, model, text.hashCode());
    }

    /**
     * 计算向量相似度
     */
    public double calculateSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        embeddingCache.clear();
        log.info("嵌入向量缓存已清除");
    }

    /**
     * 获取缓存统计
     */
    public CacheStats getCacheStats() {
        CacheStats stats = new CacheStats();
        stats.setSize(embeddingCache.size());
        stats.setMaxSize(10000); // 假设最大缓存大小
        return stats;
    }

    /**
     * 缓存统计
     */
    public static class CacheStats {
        private int size;
        private int maxSize;

        public int getSize() { return size; }
        public void setSize(int size) { this.size = size; }

        public int getMaxSize() { return maxSize; }
        public void setMaxSize(int maxSize) { this.maxSize = maxSize; }

        public double getUsageRate() {
            return maxSize > 0 ? (double) size / maxSize : 0.0;
        }
    }
}
