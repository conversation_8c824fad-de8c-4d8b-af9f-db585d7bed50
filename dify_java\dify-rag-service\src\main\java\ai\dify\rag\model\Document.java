package ai.dify.rag.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文档模型
 */
@Data
public class Document {

    /**
     * 文档ID
     */
    private String id;

    /**
     * 文档名称
     */
    private String name;

    /**
     * 文档内容
     */
    private String content;

    /**
     * 文档类型
     */
    private String type;

    /**
     * 文档大小（字节）
     */
    private Long size;

    /**
     * 文档来源
     */
    private String source;

    /**
     * 文档URL
     */
    private String url;

    /**
     * 文档元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 文档分段列表
     */
    private List<Segment> segments;

    /**
     * 文档分段
     */
    @Data
    public static class Segment {
        /**
         * 分段ID
         */
        private String id;

        /**
         * 文档ID
         */
        private String documentId;

        /**
         * 分段内容
         */
        private String content;

        /**
         * 分段位置
         */
        private Integer position;

        /**
         * 分段长度
         */
        private Integer length;

        /**
         * 向量嵌入
         */
        private float[] embedding;

        /**
         * 相似度分数
         */
        private Double score;

        /**
         * 分段元数据
         */
        private Map<String, Object> metadata;

        /**
         * 创建时间
         */
        private LocalDateTime createdAt;

        /**
         * 创建分段
         */
        public static Segment create(String documentId, String content, Integer position) {
            Segment segment = new Segment();
            segment.setId(java.util.UUID.randomUUID().toString());
            segment.setDocumentId(documentId);
            segment.setContent(content);
            segment.setPosition(position);
            segment.setLength(content.length());
            segment.setCreatedAt(LocalDateTime.now());
            return segment;
        }

        /**
         * 设置元数据
         */
        public Segment withMetadata(String key, Object value) {
            if (this.metadata == null) {
                this.metadata = new java.util.HashMap<>();
            }
            this.metadata.put(key, value);
            return this;
        }
    }

    /**
     * 创建文档
     */
    public static Document create(String name, String content, String type) {
        Document document = new Document();
        document.setId(java.util.UUID.randomUUID().toString());
        document.setName(name);
        document.setContent(content);
        document.setType(type);
        document.setSize((long) content.length());
        document.setCreatedAt(LocalDateTime.now());
        document.setUpdatedAt(LocalDateTime.now());
        return document;
    }

    /**
     * 设置元数据
     */
    public Document withMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new java.util.HashMap<>();
        }
        this.metadata.put(key, value);
        return this;
    }

    /**
     * 设置来源
     */
    public Document withSource(String source) {
        this.source = source;
        return this;
    }

    /**
     * 设置URL
     */
    public Document withUrl(String url) {
        this.url = url;
        return this;
    }

    /**
     * 获取文档字符数
     */
    public int getCharacterCount() {
        return content != null ? content.length() : 0;
    }

    /**
     * 获取分段数量
     */
    public int getSegmentCount() {
        return segments != null ? segments.size() : 0;
    }
}
