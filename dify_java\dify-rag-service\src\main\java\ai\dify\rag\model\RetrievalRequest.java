package ai.dify.rag.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import java.util.Map;

/**
 * 检索请求
 */
@Data
public class RetrievalRequest {

    /**
     * 数据集ID
     */
    @NotBlank(message = "数据集ID不能为空")
    private String datasetId;

    /**
     * 查询内容
     */
    @NotBlank(message = "查询内容不能为空")
    private String query;

    /**
     * 返回结果数量
     */
    @Positive(message = "TopK必须大于0")
    private Integer topK = 5;

    /**
     * 相似度分数阈值
     */
    private Double scoreThreshold = 0.0;

    /**
     * 检索模式
     */
    private RetrievalMode mode = RetrievalMode.VECTOR;

    /**
     * 重排序模型
     */
    private String rerankModel;

    /**
     * 重排序TopK
     */
    private Integer rerankTopK;

    /**
     * 过滤条件
     */
    private Map<String, Object> filters;

    /**
     * 额外参数
     */
    private Map<String, Object> extraParams;

    /**
     * 检索模式枚举
     */
    public enum RetrievalMode {
        /**
         * 向量检索
         */
        VECTOR,

        /**
         * 全文检索
         */
        FULLTEXT,

        /**
         * 混合检索
         */
        HYBRID
    }

    /**
     * 创建简单检索请求
     */
    public static RetrievalRequest create(String datasetId, String query) {
        RetrievalRequest request = new RetrievalRequest();
        request.setDatasetId(datasetId);
        request.setQuery(query);
        return request;
    }

    /**
     * 创建检索请求
     */
    public static RetrievalRequest create(String datasetId, String query, Integer topK) {
        RetrievalRequest request = create(datasetId, query);
        request.setTopK(topK);
        return request;
    }

    /**
     * 设置分数阈值
     */
    public RetrievalRequest withScoreThreshold(Double scoreThreshold) {
        this.scoreThreshold = scoreThreshold;
        return this;
    }

    /**
     * 设置检索模式
     */
    public RetrievalRequest withMode(RetrievalMode mode) {
        this.mode = mode;
        return this;
    }

    /**
     * 设置重排序
     */
    public RetrievalRequest withRerank(String rerankModel, Integer rerankTopK) {
        this.rerankModel = rerankModel;
        this.rerankTopK = rerankTopK;
        return this;
    }

    /**
     * 添加过滤条件
     */
    public RetrievalRequest withFilter(String key, Object value) {
        if (this.filters == null) {
            this.filters = new java.util.HashMap<>();
        }
        this.filters.put(key, value);
        return this;
    }

    /**
     * 添加额外参数
     */
    public RetrievalRequest withExtraParam(String key, Object value) {
        if (this.extraParams == null) {
            this.extraParams = new java.util.HashMap<>();
        }
        this.extraParams.put(key, value);
        return this;
    }
}
