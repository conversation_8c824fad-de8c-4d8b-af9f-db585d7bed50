package ai.dify.rag.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 检索结果
 */
@Data
public class RetrievalResult {

    /**
     * 查询内容
     */
    private String query;

    /**
     * 数据集ID
     */
    private String datasetId;

    /**
     * 检索到的分段列表
     */
    private List<Document.Segment> segments;

    /**
     * 结果数量
     */
    private Integer count;

    /**
     * 检索耗时（毫秒）
     */
    private Long duration;

    /**
     * 检索时间
     */
    private LocalDateTime retrievedAt;

    /**
     * 检索元数据
     */
    private Map<String, Object> metadata;

    public RetrievalResult() {
        this.retrievedAt = LocalDateTime.now();
    }

    /**
     * 设置检索耗时
     */
    public RetrievalResult withDuration(Long duration) {
        this.duration = duration;
        return this;
    }

    /**
     * 添加元数据
     */
    public RetrievalResult withMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new java.util.HashMap<>();
        }
        this.metadata.put(key, value);
        return this;
    }

    /**
     * 获取最高分数
     */
    public Double getMaxScore() {
        if (segments == null || segments.isEmpty()) {
            return null;
        }
        return segments.stream()
                .mapToDouble(segment -> segment.getScore() != null ? segment.getScore() : 0.0)
                .max()
                .orElse(0.0);
    }

    /**
     * 获取平均分数
     */
    public Double getAverageScore() {
        if (segments == null || segments.isEmpty()) {
            return null;
        }
        return segments.stream()
                .mapToDouble(segment -> segment.getScore() != null ? segment.getScore() : 0.0)
                .average()
                .orElse(0.0);
    }

    /**
     * 获取总字符数
     */
    public Integer getTotalCharacters() {
        if (segments == null || segments.isEmpty()) {
            return 0;
        }
        return segments.stream()
                .mapToInt(segment -> segment.getContent() != null ? segment.getContent().length() : 0)
                .sum();
    }

    /**
     * 获取合并的内容
     */
    public String getCombinedContent() {
        if (segments == null || segments.isEmpty()) {
            return "";
        }
        return segments.stream()
                .map(Document.Segment::getContent)
                .filter(content -> content != null && !content.trim().isEmpty())
                .reduce((a, b) -> a + "\n\n" + b)
                .orElse("");
    }

    /**
     * 过滤低分结果
     */
    public RetrievalResult filterByScore(double threshold) {
        if (segments != null) {
            List<Document.Segment> filtered = segments.stream()
                    .filter(segment -> segment.getScore() != null && segment.getScore() >= threshold)
                    .toList();
            this.segments = filtered;
            this.count = filtered.size();
        }
        return this;
    }

    /**
     * 限制结果数量
     */
    public RetrievalResult limitResults(int limit) {
        if (segments != null && segments.size() > limit) {
            this.segments = segments.subList(0, limit);
            this.count = limit;
        }
        return this;
    }
}
