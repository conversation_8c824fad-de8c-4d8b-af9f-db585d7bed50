package ai.dify.rag.processor;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.rag.model.Document;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 文档处理器 - 负责文档解析和分段
 */
@Slf4j
@Component
public class DocumentProcessor {

    // 默认分段配置
    private static final int DEFAULT_CHUNK_SIZE = 1000;
    private static final int DEFAULT_CHUNK_OVERLAP = 200;
    private static final Pattern SENTENCE_PATTERN = Pattern.compile("[.!?。！？]+");
    private static final Pattern PARAGRAPH_PATTERN = Pattern.compile("\n\n+");

    /**
     * 处理文档
     */
    public List<Document.Segment> processDocument(Document document) {
        log.debug("处理文档: documentId={}, type={}", document.getId(), document.getType());
        
        try {
            // 1. 解析文档内容
            String content = parseDocument(document);
            
            // 2. 清理和预处理
            content = preprocessContent(content);
            
            // 3. 文档分段
            List<Document.Segment> segments = segmentDocument(document.getId(), content);
            
            log.debug("文档处理完成: documentId={}, segments={}", document.getId(), segments.size());
            return segments;
            
        } catch (Exception e) {
            log.error("文档处理失败: documentId={}", document.getId(), e);
            throw new DifyException(ResultCode.ERROR, "文档处理失败: " + e.getMessage());
        }
    }

    /**
     * 解析文档内容
     */
    private String parseDocument(Document document) {
        String content = document.getContent();
        String type = document.getType();
        
        if (content == null || content.trim().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "文档内容不能为空");
        }
        
        // 根据文档类型进行特殊处理
        return switch (type != null ? type.toLowerCase() : "text") {
            case "pdf" -> parsePdfContent(content);
            case "html" -> parseHtmlContent(content);
            case "markdown", "md" -> parseMarkdownContent(content);
            case "json" -> parseJsonContent(content);
            case "xml" -> parseXmlContent(content);
            default -> content; // 纯文本
        };
    }

    /**
     * 预处理内容
     */
    private String preprocessContent(String content) {
        if (content == null) {
            return "";
        }
        
        // 1. 移除多余的空白字符
        content = content.replaceAll("\\s+", " ");
        
        // 2. 移除特殊字符
        content = content.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");
        
        // 3. 标准化换行符
        content = content.replaceAll("\\r\\n|\\r", "\n");
        
        // 4. 移除多余的换行
        content = content.replaceAll("\n{3,}", "\n\n");
        
        return content.trim();
    }

    /**
     * 文档分段
     */
    private List<Document.Segment> segmentDocument(String documentId, String content) {
        List<Document.Segment> segments = new ArrayList<>();
        
        // 使用滑动窗口分段
        int chunkSize = DEFAULT_CHUNK_SIZE;
        int overlap = DEFAULT_CHUNK_OVERLAP;
        
        int position = 0;
        int segmentIndex = 0;
        
        while (position < content.length()) {
            int endPos = Math.min(position + chunkSize, content.length());
            
            // 尝试在句子边界分割
            if (endPos < content.length()) {
                int sentenceEnd = findSentenceBoundary(content, position, endPos);
                if (sentenceEnd > position) {
                    endPos = sentenceEnd;
                }
            }
            
            String segmentContent = content.substring(position, endPos).trim();
            
            if (!segmentContent.isEmpty()) {
                Document.Segment segment = Document.Segment.create(documentId, segmentContent, segmentIndex);
                segment.withMetadata("start_pos", position)
                       .withMetadata("end_pos", endPos)
                       .withMetadata("chunk_size", chunkSize)
                       .withMetadata("overlap", overlap);
                
                segments.add(segment);
                segmentIndex++;
            }
            
            // 移动到下一个位置（考虑重叠）
            position = Math.max(position + 1, endPos - overlap);
        }
        
        return segments;
    }

    /**
     * 查找句子边界
     */
    private int findSentenceBoundary(String content, int start, int end) {
        // 在指定范围内查找最后一个句子结束符
        for (int i = end - 1; i > start; i--) {
            char c = content.charAt(i);
            if (c == '.' || c == '!' || c == '?' || c == '。' || c == '！' || c == '？') {
                // 确保不是缩写或数字
                if (i + 1 < content.length() && Character.isWhitespace(content.charAt(i + 1))) {
                    return i + 1;
                }
            }
        }
        
        // 如果没有找到句子边界，查找段落边界
        for (int i = end - 1; i > start; i--) {
            if (content.charAt(i) == '\n') {
                return i + 1;
            }
        }
        
        return end;
    }

    /**
     * 解析PDF内容
     */
    private String parsePdfContent(String content) {
        // TODO: 使用Apache Tika或其他PDF解析库
        // 这里简化处理
        return content;
    }

    /**
     * 解析HTML内容
     */
    private String parseHtmlContent(String content) {
        // 简单的HTML标签移除
        return content.replaceAll("<[^>]+>", "")
                     .replaceAll("&nbsp;", " ")
                     .replaceAll("&amp;", "&")
                     .replaceAll("&lt;", "<")
                     .replaceAll("&gt;", ">")
                     .replaceAll("&quot;", "\"");
    }

    /**
     * 解析Markdown内容
     */
    private String parseMarkdownContent(String content) {
        // 移除Markdown语法，保留文本内容
        return content.replaceAll("^#{1,6}\\s+", "") // 标题
                     .replaceAll("\\*\\*(.*?)\\*\\*", "$1") // 粗体
                     .replaceAll("\\*(.*?)\\*", "$1") // 斜体
                     .replaceAll("`(.*?)`", "$1") // 行内代码
                     .replaceAll("```[\\s\\S]*?```", "") // 代码块
                     .replaceAll("\\[([^\\]]+)\\]\\([^\\)]+\\)", "$1") // 链接
                     .replaceAll("!\\[([^\\]]*)\\]\\([^\\)]+\\)", "$1"); // 图片
    }

    /**
     * 解析JSON内容
     */
    private String parseJsonContent(String content) {
        // TODO: 解析JSON结构，提取有意义的文本
        return content;
    }

    /**
     * 解析XML内容
     */
    private String parseXmlContent(String content) {
        // 简单的XML标签移除
        return content.replaceAll("<[^>]+>", "");
    }

    /**
     * 自定义分段配置
     */
    public List<Document.Segment> processDocumentWithConfig(Document document, 
                                                           int chunkSize, 
                                                           int overlap) {
        log.debug("使用自定义配置处理文档: documentId={}, chunkSize={}, overlap={}", 
                document.getId(), chunkSize, overlap);
        
        try {
            String content = parseDocument(document);
            content = preprocessContent(content);
            
            return segmentDocumentWithConfig(document.getId(), content, chunkSize, overlap);
            
        } catch (Exception e) {
            log.error("文档处理失败: documentId={}", document.getId(), e);
            throw new DifyException(ResultCode.ERROR, "文档处理失败: " + e.getMessage());
        }
    }

    /**
     * 使用自定义配置分段
     */
    private List<Document.Segment> segmentDocumentWithConfig(String documentId, 
                                                           String content, 
                                                           int chunkSize, 
                                                           int overlap) {
        List<Document.Segment> segments = new ArrayList<>();
        
        int position = 0;
        int segmentIndex = 0;
        
        while (position < content.length()) {
            int endPos = Math.min(position + chunkSize, content.length());
            
            if (endPos < content.length()) {
                int sentenceEnd = findSentenceBoundary(content, position, endPos);
                if (sentenceEnd > position) {
                    endPos = sentenceEnd;
                }
            }
            
            String segmentContent = content.substring(position, endPos).trim();
            
            if (!segmentContent.isEmpty()) {
                Document.Segment segment = Document.Segment.create(documentId, segmentContent, segmentIndex);
                segment.withMetadata("start_pos", position)
                       .withMetadata("end_pos", endPos)
                       .withMetadata("chunk_size", chunkSize)
                       .withMetadata("overlap", overlap);
                
                segments.add(segment);
                segmentIndex++;
            }
            
            position = Math.max(position + 1, endPos - overlap);
        }
        
        return segments;
    }
}
