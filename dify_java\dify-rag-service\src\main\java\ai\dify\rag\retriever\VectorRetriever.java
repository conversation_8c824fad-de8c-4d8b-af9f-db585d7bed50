package ai.dify.rag.retriever;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.rag.model.Document;
import ai.dify.rag.service.RAGService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 向量检索器 - 负责向量存储和检索
 */
@Slf4j
@Component
public class VectorRetriever {

    // 内存向量存储（生产环境应使用专业的向量数据库）
    private final Map<String, Map<String, Document.Segment>> datasetSegments = new ConcurrentHashMap<>();
    private final Map<String, RAGService.DatasetStats> datasetStats = new ConcurrentHashMap<>();

    /**
     * 添加分段到向量存储
     */
    public void addSegments(String datasetId, List<Document.Segment> segments) {
        log.debug("添加分段到向量存储: datasetId={}, count={}", datasetId, segments.size());
        
        try {
            Map<String, Document.Segment> segmentMap = datasetSegments.computeIfAbsent(
                    datasetId, k -> new ConcurrentHashMap<>());
            
            for (Document.Segment segment : segments) {
                if (segment.getEmbedding() == null) {
                    log.warn("分段缺少嵌入向量，跳过: segmentId={}", segment.getId());
                    continue;
                }
                
                segmentMap.put(segment.getId(), segment);
            }
            
            // 更新统计信息
            updateDatasetStats(datasetId);
            
            log.debug("分段添加完成: datasetId={}, total={}", datasetId, segmentMap.size());
            
        } catch (Exception e) {
            log.error("添加分段失败: datasetId={}", datasetId, e);
            throw new DifyException(ResultCode.VECTOR_STORE_ERROR, "添加分段失败: " + e.getMessage());
        }
    }

    /**
     * 向量检索
     */
    public List<Document.Segment> retrieve(String datasetId, float[] queryEmbedding, 
                                         Integer topK, Double scoreThreshold) {
        log.debug("执行向量检索: datasetId={}, topK={}, threshold={}", datasetId, topK, scoreThreshold);
        
        try {
            Map<String, Document.Segment> segmentMap = datasetSegments.get(datasetId);
            if (segmentMap == null || segmentMap.isEmpty()) {
                log.warn("数据集为空: datasetId={}", datasetId);
                return Collections.emptyList();
            }
            
            // 计算相似度并排序
            List<ScoredSegment> scoredSegments = new ArrayList<>();
            
            for (Document.Segment segment : segmentMap.values()) {
                if (segment.getEmbedding() == null) {
                    continue;
                }
                
                double similarity = calculateCosineSimilarity(queryEmbedding, segment.getEmbedding());
                
                if (scoreThreshold == null || similarity >= scoreThreshold) {
                    scoredSegments.add(new ScoredSegment(segment, similarity));
                }
            }
            
            // 按相似度降序排序
            scoredSegments.sort((a, b) -> Double.compare(b.score, a.score));
            
            // 限制返回数量
            int limit = topK != null ? Math.min(topK, scoredSegments.size()) : scoredSegments.size();
            
            List<Document.Segment> results = scoredSegments.stream()
                    .limit(limit)
                    .map(scored -> {
                        Document.Segment segment = scored.segment;
                        segment.setScore(scored.score);
                        return segment;
                    })
                    .collect(Collectors.toList());
            
            log.debug("检索完成: datasetId={}, found={}", datasetId, results.size());
            return results;
            
        } catch (Exception e) {
            log.error("向量检索失败: datasetId={}", datasetId, e);
            throw new DifyException(ResultCode.RETRIEVAL_ERROR, "向量检索失败: " + e.getMessage());
        }
    }

    /**
     * 删除文档
     */
    public void deleteDocument(String datasetId, String documentId) {
        log.debug("删除文档: datasetId={}, documentId={}", datasetId, documentId);
        
        try {
            Map<String, Document.Segment> segmentMap = datasetSegments.get(datasetId);
            if (segmentMap == null) {
                return;
            }
            
            // 删除属于该文档的所有分段
            List<String> segmentIdsToDelete = segmentMap.values().stream()
                    .filter(segment -> documentId.equals(segment.getDocumentId()))
                    .map(Document.Segment::getId)
                    .collect(Collectors.toList());
            
            for (String segmentId : segmentIdsToDelete) {
                segmentMap.remove(segmentId);
            }
            
            // 更新统计信息
            updateDatasetStats(datasetId);
            
            log.debug("文档删除完成: documentId={}, deletedSegments={}", documentId, segmentIdsToDelete.size());
            
        } catch (Exception e) {
            log.error("删除文档失败: documentId={}", documentId, e);
            throw new DifyException(ResultCode.VECTOR_STORE_ERROR, "删除文档失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据集统计信息
     */
    public RAGService.DatasetStats getDatasetStats(String datasetId) {
        return datasetStats.get(datasetId);
    }

    /**
     * 清空数据集
     */
    public void clearDataset(String datasetId) {
        log.debug("清空数据集: datasetId={}", datasetId);
        
        try {
            datasetSegments.remove(datasetId);
            datasetStats.remove(datasetId);
            
            log.debug("数据集清空完成: datasetId={}", datasetId);
            
        } catch (Exception e) {
            log.error("清空数据集失败: datasetId={}", datasetId, e);
            throw new DifyException(ResultCode.VECTOR_STORE_ERROR, "清空数据集失败: " + e.getMessage());
        }
    }

    /**
     * 计算余弦相似度
     */
    private double calculateCosineSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 更新数据集统计信息
     */
    private void updateDatasetStats(String datasetId) {
        Map<String, Document.Segment> segmentMap = datasetSegments.get(datasetId);
        if (segmentMap == null) {
            return;
        }

        // 统计文档数量
        Set<String> documentIds = segmentMap.values().stream()
                .map(Document.Segment::getDocumentId)
                .collect(Collectors.toSet());

        // 统计字符数
        long totalCharacters = segmentMap.values().stream()
                .mapToLong(segment -> segment.getContent() != null ? segment.getContent().length() : 0)
                .sum();

        RAGService.DatasetStats stats = new RAGService.DatasetStats();
        stats.setDatasetId(datasetId);
        stats.setDocumentCount(documentIds.size());
        stats.setSegmentCount(segmentMap.size());
        stats.setTotalCharacters(totalCharacters);
        stats.setLastUpdated(LocalDateTime.now());

        datasetStats.put(datasetId, stats);
    }

    /**
     * 获取所有数据集ID
     */
    public Set<String> getAllDatasetIds() {
        return new HashSet<>(datasetSegments.keySet());
    }

    /**
     * 检查数据集是否存在
     */
    public boolean datasetExists(String datasetId) {
        return datasetSegments.containsKey(datasetId);
    }

    /**
     * 获取数据集中的分段数量
     */
    public int getSegmentCount(String datasetId) {
        Map<String, Document.Segment> segmentMap = datasetSegments.get(datasetId);
        return segmentMap != null ? segmentMap.size() : 0;
    }

    /**
     * 带分数的分段
     */
    private static class ScoredSegment {
        final Document.Segment segment;
        final double score;

        ScoredSegment(Document.Segment segment, double score) {
            this.segment = segment;
            this.score = score;
        }
    }
}
