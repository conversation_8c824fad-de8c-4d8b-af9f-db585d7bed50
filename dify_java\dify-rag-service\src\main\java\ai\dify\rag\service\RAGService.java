package ai.dify.rag.service;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.rag.model.Document;
import ai.dify.rag.model.RetrievalRequest;
import ai.dify.rag.model.RetrievalResult;
import ai.dify.rag.processor.DocumentProcessor;
import ai.dify.rag.retriever.VectorRetriever;
import ai.dify.rag.embedder.EmbeddingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * RAG服务 - 检索增强生成
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RAGService {

    private final DocumentProcessor documentProcessor;
    private final EmbeddingService embeddingService;
    private final VectorRetriever vectorRetriever;

    /**
     * 添加文档到知识库
     */
    public CompletableFuture<String> addDocument(String datasetId, Document document) {
        log.info("添加文档到知识库: datasetId={}, documentId={}", datasetId, document.getId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 处理文档（解析、分段）
                List<Document.Segment> segments = documentProcessor.processDocument(document);
                
                // 2. 生成向量嵌入
                for (Document.Segment segment : segments) {
                    float[] embedding = embeddingService.embed(segment.getContent());
                    segment.setEmbedding(embedding);
                }
                
                // 3. 存储到向量数据库
                vectorRetriever.addSegments(datasetId, segments);
                
                log.info("文档添加成功: documentId={}, segments={}", document.getId(), segments.size());
                return document.getId();
                
            } catch (Exception e) {
                log.error("添加文档失败: documentId={}", document.getId(), e);
                throw new DifyException(ResultCode.ERROR, "添加文档失败: " + e.getMessage());
            }
        });
    }

    /**
     * 批量添加文档
     */
    public CompletableFuture<List<String>> addDocuments(String datasetId, List<Document> documents) {
        log.info("批量添加文档: datasetId={}, count={}", datasetId, documents.size());
        
        List<CompletableFuture<String>> futures = documents.stream()
                .map(doc -> addDocument(datasetId, doc))
                .toList();
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .toList());
    }

    /**
     * 检索相关文档
     */
    public RetrievalResult retrieve(RetrievalRequest request) {
        log.debug("检索文档: datasetId={}, query={}", request.getDatasetId(), request.getQuery());
        
        try {
            // 1. 验证请求
            validateRetrievalRequest(request);
            
            // 2. 生成查询向量
            float[] queryEmbedding = embeddingService.embed(request.getQuery());
            
            // 3. 向量检索
            List<Document.Segment> segments = vectorRetriever.retrieve(
                    request.getDatasetId(),
                    queryEmbedding,
                    request.getTopK(),
                    request.getScoreThreshold()
            );
            
            // 4. 构建结果
            RetrievalResult result = new RetrievalResult();
            result.setQuery(request.getQuery());
            result.setDatasetId(request.getDatasetId());
            result.setSegments(segments);
            result.setCount(segments.size());
            
            log.debug("检索完成: datasetId={}, found={}", request.getDatasetId(), segments.size());
            return result;
            
        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("检索失败: datasetId={}, query={}", request.getDatasetId(), request.getQuery(), e);
            throw new DifyException(ResultCode.RETRIEVAL_ERROR, "检索失败: " + e.getMessage());
        }
    }

    /**
     * 删除文档
     */
    public void deleteDocument(String datasetId, String documentId) {
        log.info("删除文档: datasetId={}, documentId={}", datasetId, documentId);
        
        try {
            vectorRetriever.deleteDocument(datasetId, documentId);
            log.info("文档删除成功: documentId={}", documentId);
        } catch (Exception e) {
            log.error("删除文档失败: documentId={}", documentId, e);
            throw new DifyException(ResultCode.ERROR, "删除文档失败: " + e.getMessage());
        }
    }

    /**
     * 更新文档
     */
    public CompletableFuture<String> updateDocument(String datasetId, Document document) {
        log.info("更新文档: datasetId={}, documentId={}", datasetId, document.getId());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 1. 删除旧文档
                deleteDocument(datasetId, document.getId());
                
                // 2. 添加新文档
                return addDocument(datasetId, document).join();
                
            } catch (Exception e) {
                log.error("更新文档失败: documentId={}", document.getId(), e);
                throw new DifyException(ResultCode.ERROR, "更新文档失败: " + e.getMessage());
            }
        });
    }

    /**
     * 获取数据集统计信息
     */
    public DatasetStats getDatasetStats(String datasetId) {
        log.debug("获取数据集统计: datasetId={}", datasetId);
        
        try {
            return vectorRetriever.getDatasetStats(datasetId);
        } catch (Exception e) {
            log.error("获取数据集统计失败: datasetId={}", datasetId, e);
            throw new DifyException(ResultCode.ERROR, "获取数据集统计失败: " + e.getMessage());
        }
    }

    /**
     * 清空数据集
     */
    public void clearDataset(String datasetId) {
        log.info("清空数据集: datasetId={}", datasetId);
        
        try {
            vectorRetriever.clearDataset(datasetId);
            log.info("数据集清空成功: datasetId={}", datasetId);
        } catch (Exception e) {
            log.error("清空数据集失败: datasetId={}", datasetId, e);
            throw new DifyException(ResultCode.ERROR, "清空数据集失败: " + e.getMessage());
        }
    }

    /**
     * 验证检索请求
     */
    private void validateRetrievalRequest(RetrievalRequest request) {
        if (request == null) {
            throw new DifyException(ResultCode.PARAM_ERROR, "检索请求不能为空");
        }
        
        if (request.getDatasetId() == null || request.getDatasetId().trim().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "数据集ID不能为空");
        }
        
        if (request.getQuery() == null || request.getQuery().trim().isEmpty()) {
            throw new DifyException(ResultCode.PARAM_ERROR, "查询内容不能为空");
        }
        
        if (request.getTopK() != null && request.getTopK() <= 0) {
            throw new DifyException(ResultCode.PARAM_ERROR, "TopK必须大于0");
        }
        
        if (request.getScoreThreshold() != null && 
            (request.getScoreThreshold() < 0 || request.getScoreThreshold() > 1)) {
            throw new DifyException(ResultCode.PARAM_ERROR, "分数阈值必须在0-1之间");
        }
    }

    /**
     * 数据集统计信息
     */
    public static class DatasetStats {
        private String datasetId;
        private long documentCount;
        private long segmentCount;
        private long totalCharacters;
        private java.time.LocalDateTime lastUpdated;

        // Getters and Setters
        public String getDatasetId() { return datasetId; }
        public void setDatasetId(String datasetId) { this.datasetId = datasetId; }

        public long getDocumentCount() { return documentCount; }
        public void setDocumentCount(long documentCount) { this.documentCount = documentCount; }

        public long getSegmentCount() { return segmentCount; }
        public void setSegmentCount(long segmentCount) { this.segmentCount = segmentCount; }

        public long getTotalCharacters() { return totalCharacters; }
        public void setTotalCharacters(long totalCharacters) { this.totalCharacters = totalCharacters; }

        public java.time.LocalDateTime getLastUpdated() { return lastUpdated; }
        public void setLastUpdated(java.time.LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }
    }
}
