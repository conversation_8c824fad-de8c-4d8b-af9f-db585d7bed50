package ai.dify.rag.service;

import ai.dify.rag.embedder.EmbeddingService;
import ai.dify.rag.model.Document;
import ai.dify.rag.model.RetrievalRequest;
import ai.dify.rag.model.RetrievalResult;
import ai.dify.rag.processor.DocumentProcessor;
import ai.dify.rag.retriever.VectorRetriever;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RAG服务测试
 */
@ExtendWith(MockitoExtension.class)
class RAGServiceTest {

    @Mock
    private DocumentProcessor documentProcessor;

    @Mock
    private EmbeddingService embeddingService;

    @Mock
    private VectorRetriever vectorRetriever;

    @InjectMocks
    private RAGService ragService;

    private String datasetId;
    private Document document;
    private RetrievalRequest retrievalRequest;

    @BeforeEach
    void setUp() {
        datasetId = "dataset-123";
        
        document = Document.create("test.txt", "This is a test document content.", "text");
        document.setId("doc-123");

        retrievalRequest = RetrievalRequest.create(datasetId, "test query");
        retrievalRequest.setTopK(5);
        retrievalRequest.setScoreThreshold(0.5);
    }

    @Test
    void addDocument_Success() throws Exception {
        // Given
        Document.Segment segment = Document.Segment.create(document.getId(), "test content", 0);
        List<Document.Segment> segments = List.of(segment);
        float[] embedding = new float[]{0.1f, 0.2f, 0.3f};

        when(documentProcessor.processDocument(document)).thenReturn(segments);
        when(embeddingService.embed(anyString())).thenReturn(embedding);
        doNothing().when(vectorRetriever).addSegments(eq(datasetId), anyList());

        // When
        CompletableFuture<String> future = ragService.addDocument(datasetId, document);
        String result = future.get();

        // Then
        assertEquals(document.getId(), result);
        verify(documentProcessor).processDocument(document);
        verify(embeddingService).embed("test content");
        verify(vectorRetriever).addSegments(eq(datasetId), anyList());
    }

    @Test
    void retrieve_Success() {
        // Given
        float[] queryEmbedding = new float[]{0.1f, 0.2f, 0.3f};
        Document.Segment segment = Document.Segment.create(document.getId(), "test content", 0);
        segment.setScore(0.8);
        List<Document.Segment> segments = List.of(segment);

        when(embeddingService.embed(retrievalRequest.getQuery())).thenReturn(queryEmbedding);
        when(vectorRetriever.retrieve(
            eq(datasetId), 
            eq(queryEmbedding), 
            eq(retrievalRequest.getTopK()), 
            eq(retrievalRequest.getScoreThreshold())
        )).thenReturn(segments);

        // When
        RetrievalResult result = ragService.retrieve(retrievalRequest);

        // Then
        assertNotNull(result);
        assertEquals(retrievalRequest.getQuery(), result.getQuery());
        assertEquals(datasetId, result.getDatasetId());
        assertEquals(1, result.getCount());
        assertEquals(1, result.getSegments().size());
        assertEquals(0.8, result.getSegments().get(0).getScore());

        verify(embeddingService).embed(retrievalRequest.getQuery());
        verify(vectorRetriever).retrieve(
            eq(datasetId), 
            eq(queryEmbedding), 
            eq(retrievalRequest.getTopK()), 
            eq(retrievalRequest.getScoreThreshold())
        );
    }

    @Test
    void deleteDocument_Success() {
        // Given
        doNothing().when(vectorRetriever).deleteDocument(datasetId, document.getId());

        // When
        ragService.deleteDocument(datasetId, document.getId());

        // Then
        verify(vectorRetriever).deleteDocument(datasetId, document.getId());
    }

    @Test
    void getDatasetStats_Success() {
        // Given
        RAGService.DatasetStats stats = new RAGService.DatasetStats();
        stats.setDatasetId(datasetId);
        stats.setDocumentCount(10);
        stats.setSegmentCount(100);
        stats.setTotalCharacters(10000);

        when(vectorRetriever.getDatasetStats(datasetId)).thenReturn(stats);

        // When
        RAGService.DatasetStats result = ragService.getDatasetStats(datasetId);

        // Then
        assertNotNull(result);
        assertEquals(datasetId, result.getDatasetId());
        assertEquals(10, result.getDocumentCount());
        assertEquals(100, result.getSegmentCount());
        assertEquals(10000, result.getTotalCharacters());

        verify(vectorRetriever).getDatasetStats(datasetId);
    }

    @Test
    void clearDataset_Success() {
        // Given
        doNothing().when(vectorRetriever).clearDataset(datasetId);

        // When
        ragService.clearDataset(datasetId);

        // Then
        verify(vectorRetriever).clearDataset(datasetId);
    }
}
