package ai.dify.security.filter;

import ai.dify.security.service.JwtService;
import ai.dify.security.service.UserDetailsServiceImpl;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT 认证过滤器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtService jwtService;
    private final UserDetailsServiceImpl userDetailsService;

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            String jwt = getJwtFromRequest(request);
            
            if (StringUtils.hasText(jwt)) {
                if (jwtService.validateToken(jwt)) {
                    String username = jwtService.getUsernameFromToken(jwt);
                    
                    if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                        
                        if (userDetails != null) {
                            UsernamePasswordAuthenticationToken authentication = 
                                new UsernamePasswordAuthenticationToken(
                                    userDetails, null, userDetails.getAuthorities());
                            
                            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                            
                            log.debug("用户认证成功: {}", username);
                        }
                    }
                } else {
                    log.debug("JWT令牌验证失败");
                }
            } else {
                // 检查是否是API密钥认证
                String apiKey = getApiKeyFromRequest(request);
                if (StringUtils.hasText(apiKey)) {
                    authenticateWithApiKey(request, apiKey);
                }
            }
            
        } catch (Exception e) {
            log.error("JWT认证过滤器异常", e);
        }
        
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取JWT令牌
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
        
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(BEARER_PREFIX)) {
            return bearerToken.substring(BEARER_PREFIX.length());
        }
        
        return null;
    }

    /**
     * 从请求中获取API密钥
     */
    private String getApiKeyFromRequest(HttpServletRequest request) {
        String authorization = request.getHeader(AUTHORIZATION_HEADER);
        
        if (StringUtils.hasText(authorization) && authorization.startsWith("Bearer ")) {
            String token = authorization.substring(7);
            // 简单判断是否是API密钥格式（不是JWT格式）
            if (!token.contains(".")) {
                return token;
            }
            
            // 或者尝试验证是否是API密钥类型的JWT
            try {
                if (jwtService.validateApiKey(token)) {
                    return token;
                }
            } catch (Exception e) {
                // 忽略异常，可能不是API密钥
            }
        }
        
        return null;
    }

    /**
     * 使用API密钥进行认证
     */
    private void authenticateWithApiKey(HttpServletRequest request, String apiKey) {
        try {
            if (jwtService.validateApiKey(apiKey)) {
                String userId = jwtService.getUserIdFromApiKey(apiKey);
                String appId = jwtService.getAppIdFromApiKey(apiKey);
                
                if (userId != null) {
                    // 创建API密钥认证对象
                    ApiKeyAuthentication authentication = new ApiKeyAuthentication(apiKey, userId, appId);
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    
                    log.debug("API密钥认证成功: userId={}, appId={}", userId, appId);
                }
            }
        } catch (Exception e) {
            log.debug("API密钥认证失败", e);
        }
    }

    /**
     * API密钥认证对象
     */
    public static class ApiKeyAuthentication extends UsernamePasswordAuthenticationToken {
        private final String apiKey;
        private final String userId;
        private final String appId;

        public ApiKeyAuthentication(String apiKey, String userId, String appId) {
            super(userId, apiKey, java.util.List.of(
                new org.springframework.security.core.authority.SimpleGrantedAuthority("ROLE_API")
            ));
            this.apiKey = apiKey;
            this.userId = userId;
            this.appId = appId;
            setAuthenticated(true);
        }

        public String getApiKey() { return apiKey; }
        public String getUserId() { return userId; }
        public String getAppId() { return appId; }
    }
}
