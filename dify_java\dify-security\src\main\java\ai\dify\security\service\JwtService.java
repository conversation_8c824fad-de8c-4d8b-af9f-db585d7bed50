package ai.dify.security.service;

import ai.dify.security.model.UserPrincipal;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * JWT 服务
 */
@Slf4j
@Service
public class JwtService {

    @Value("${dify.security.jwt.secret:dify-java-secret-key-change-in-production}")
    private String jwtSecret;

    @Value("${dify.security.jwt.expiration:86400}") // 24小时
    private int jwtExpirationInSeconds;

    @Value("${dify.security.jwt.refresh-expiration:604800}") // 7天
    private int refreshExpirationInSeconds;

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        return generateAccessToken(userPrincipal);
    }

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(UserPrincipal userPrincipal) {
        Instant now = Instant.now();
        Instant expiration = now.plus(jwtExpirationInSeconds, ChronoUnit.SECONDS);

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userPrincipal.getId());
        claims.put("email", userPrincipal.getEmail());
        claims.put("roles", userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList()));

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(userPrincipal.getUsername())
                .setIssuedAt(Date.from(now))
                .setExpiration(Date.from(expiration))
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(UserPrincipal userPrincipal) {
        Instant now = Instant.now();
        Instant expiration = now.plus(refreshExpirationInSeconds, ChronoUnit.SECONDS);

        return Jwts.builder()
                .setSubject(userPrincipal.getUsername())
                .setIssuedAt(Date.from(now))
                .setExpiration(Date.from(expiration))
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从令牌获取用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }

    /**
     * 从令牌获取用户ID
     */
    public String getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("userId", String.class);
    }

    /**
     * 从令牌获取邮箱
     */
    public String getEmailFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("email", String.class);
    }

    /**
     * 从令牌获取角色
     */
    @SuppressWarnings("unchecked")
    public java.util.List<String> getRolesFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("roles", java.util.List.class);
    }

    /**
     * 获取令牌过期时间
     */
    public Date getExpirationFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }

    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        try {
            getClaimsFromToken(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.debug("JWT令牌验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 刷新令牌
     */
    public String refreshToken(String refreshToken, UserPrincipal userPrincipal) {
        if (validateToken(refreshToken) && !isTokenExpired(refreshToken)) {
            return generateAccessToken(userPrincipal);
        }
        throw new JwtException("刷新令牌无效或已过期");
    }

    /**
     * 从令牌获取声明
     */
    private Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 生成API密钥
     */
    public String generateApiKey(String appId, String userId) {
        Instant now = Instant.now();
        // API密钥不设置过期时间，或设置很长的过期时间
        Instant expiration = now.plus(365, ChronoUnit.DAYS); // 1年

        Map<String, Object> claims = new HashMap<>();
        claims.put("appId", appId);
        claims.put("userId", userId);
        claims.put("type", "api_key");

        return Jwts.builder()
                .setClaims(claims)
                .setSubject("api_key")
                .setIssuedAt(Date.from(now))
                .setExpiration(Date.from(expiration))
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 验证API密钥
     */
    public boolean validateApiKey(String apiKey) {
        try {
            Claims claims = getClaimsFromToken(apiKey);
            String type = claims.get("type", String.class);
            return "api_key".equals(type) && !isTokenExpired(apiKey);
        } catch (Exception e) {
            log.debug("API密钥验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从API密钥获取应用ID
     */
    public String getAppIdFromApiKey(String apiKey) {
        Claims claims = getClaimsFromToken(apiKey);
        return claims.get("appId", String.class);
    }

    /**
     * 从API密钥获取用户ID
     */
    public String getUserIdFromApiKey(String apiKey) {
        Claims claims = getClaimsFromToken(apiKey);
        return claims.get("userId", String.class);
    }
}
