package ai.dify.security.service;

import ai.dify.domain.entity.User;
import ai.dify.domain.repository.UserRepository;
import ai.dify.security.model.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 用户详情服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("加载用户详情: username={}", username);
        
        User user = userRepository.findByEmail(username);
        if (user == null) {
            log.debug("用户不存在: username={}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        
        if (!user.getIsActive()) {
            log.debug("用户已禁用: username={}", username);
            throw new UsernameNotFoundException("用户已禁用: " + username);
        }
        
        log.debug("用户详情加载成功: username={}, userId={}", username, user.getId());
        return new UserPrincipal(user);
    }

    /**
     * 根据用户ID加载用户详情
     */
    public UserDetails loadUserById(String userId) throws UsernameNotFoundException {
        log.debug("根据ID加载用户详情: userId={}", userId);
        
        User user = userRepository.selectById(userId);
        if (user == null) {
            log.debug("用户不存在: userId={}", userId);
            throw new UsernameNotFoundException("用户不存在: " + userId);
        }
        
        if (!user.getIsActive()) {
            log.debug("用户已禁用: userId={}", userId);
            throw new UsernameNotFoundException("用户已禁用: " + userId);
        }
        
        log.debug("用户详情加载成功: userId={}, email={}", userId, user.getEmail());
        return new UserPrincipal(user);
    }
}
