package ai.dify.service;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Service API应用启动类
 */
@SpringBootApplication
@ComponentScan(basePackages = {"ai.dify.service", "ai.dify.common", "ai.dify.domain"})
@MapperScan("ai.dify.domain.repository")
public class ServiceApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ServiceApiApplication.class, args);
    }
}
