package ai.dify.service.controller;

import ai.dify.common.result.Result;
import ai.dify.file.model.FileInfo;
import ai.dify.file.model.FileUploadResult;
import ai.dify.file.service.FileService;
import ai.dify.service.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.io.InputStream;
import java.util.List;

/**
 * 文件控制器
 */
@Slf4j
@RestController
@RequestMapping("/v1/files")
@RequiredArgsConstructor
@Validated
@Tag(name = "文件服务", description = "文件上传下载接口")
public class FileController {

    private final FileService fileService;
    private final AuthService authService;

    @Operation(summary = "上传文件", description = "上传文件到系统")
    @PostMapping("/upload")
    public Result<FileUploadResult> uploadFile(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "用户标识", required = true) @RequestParam @NotBlank String user,
            @Parameter(description = "文件", required = true) @RequestParam("file") MultipartFile file) {
        
        log.info("收到文件上传请求: user={}, fileName={}, size={}", user, file.getOriginalFilename(), file.getSize());
        
        // 验证API密钥
        authService.validateApiKey(authorization);
        
        FileUploadResult result = fileService.uploadFile(file, user);
        return Result.success(result);
    }

    @Operation(summary = "获取文件信息", description = "获取文件的详细信息")
    @GetMapping("/{fileId}")
    public Result<FileInfo> getFileInfo(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "文件ID", required = true) @PathVariable @NotBlank String fileId) {
        
        log.info("获取文件信息: fileId={}", fileId);
        
        // 验证API密钥
        authService.validateApiKey(authorization);
        
        FileInfo fileInfo = fileService.getFileInfo(fileId);
        return Result.success(fileInfo);
    }

    @Operation(summary = "下载文件", description = "下载指定的文件")
    @GetMapping("/{fileId}/download")
    public ResponseEntity<InputStreamResource> downloadFile(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "文件ID", required = true) @PathVariable @NotBlank String fileId) {
        
        log.info("下载文件: fileId={}", fileId);
        
        // 验证API密钥
        authService.validateApiKey(authorization);
        
        // 获取文件信息
        FileInfo fileInfo = fileService.getFileInfo(fileId);
        
        // 获取文件流
        InputStream inputStream = fileService.downloadFile(fileId);
        InputStreamResource resource = new InputStreamResource(inputStream);
        
        // 构建响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileInfo.getFileName() + "\"");
        headers.add(HttpHeaders.CONTENT_TYPE, fileInfo.getMimeType());
        
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(fileInfo.getSize())
                .body(resource);
    }

    @Operation(summary = "删除文件", description = "删除指定的文件")
    @DeleteMapping("/{fileId}")
    public Result<Void> deleteFile(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "文件ID", required = true) @PathVariable @NotBlank String fileId) {
        
        log.info("删除文件: fileId={}", fileId);
        
        // 验证API密钥
        authService.validateApiKey(authorization);
        
        fileService.deleteFile(fileId);
        return Result.success();
    }

    @Operation(summary = "获取用户文件列表", description = "获取用户上传的文件列表")
    @GetMapping
    public Result<List<FileInfo>> getUserFiles(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "用户标识", required = true) @RequestParam @NotBlank String user,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        
        log.info("获取用户文件列表: user={}, page={}, size={}", user, page, size);
        
        // 验证API密钥
        authService.validateApiKey(authorization);
        
        List<FileInfo> files = fileService.getUserFiles(user, page, size);
        return Result.success(files);
    }
}
