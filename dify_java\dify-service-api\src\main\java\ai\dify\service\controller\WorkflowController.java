package ai.dify.service.controller;

import ai.dify.common.result.Result;
import ai.dify.service.dto.WorkflowExecuteRequest;
import ai.dify.service.dto.WorkflowExecuteResponse;
import ai.dify.service.service.WorkflowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 工作流控制器
 */
@Slf4j
@RestController
@RequestMapping("/v1/workflows")
@RequiredArgsConstructor
@Validated
@Tag(name = "工作流服务", description = "AI工作流执行接口")
public class WorkflowController {

    private final WorkflowService workflowService;

    @Operation(summary = "执行工作流", description = "执行AI工作流并返回结果")
    @PostMapping("/run")
    public Result<WorkflowExecuteResponse> runWorkflow(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "工作流执行请求", required = true) @Valid @RequestBody WorkflowExecuteRequest request) {
        
        log.info("收到工作流执行请求: user={}, inputs={}", request.getUser(), request.getInputs());
        
        WorkflowExecuteResponse response = workflowService.executeWorkflow(authorization, request);
        return Result.success(response);
    }

    @Operation(summary = "流式执行工作流", description = "流式执行AI工作流并返回实时结果")
    @PostMapping(value = "/stream-run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> runWorkflowStream(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "工作流执行请求", required = true) @Valid @RequestBody WorkflowExecuteRequest request) {
        
        log.info("收到流式工作流执行请求: user={}, inputs={}", request.getUser(), request.getInputs());
        
        return workflowService.executeWorkflowStream(authorization, request);
    }

    @Operation(summary = "停止工作流执行", description = "停止正在执行的工作流")
    @PostMapping("/{taskId}/stop")
    public Result<Void> stopWorkflow(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "任务ID", required = true) @PathVariable @NotBlank String taskId) {
        
        log.info("停止工作流执行: taskId={}", taskId);
        
        workflowService.stopWorkflowExecution(authorization, taskId);
        return Result.success();
    }

    @Operation(summary = "获取工作流执行状态", description = "获取工作流的执行状态")
    @GetMapping("/{executionId}/status")
    public Result<Map<String, Object>> getWorkflowStatus(
            @Parameter(description = "API密钥", required = true) @RequestHeader("Authorization") @NotBlank String authorization,
            @Parameter(description = "执行ID", required = true) @PathVariable @NotBlank String executionId) {
        
        log.info("获取工作流执行状态: executionId={}", executionId);
        
        Map<String, Object> status = workflowService.getWorkflowExecutionStatus(authorization, executionId);
        return Result.success(status);
    }
}
