package ai.dify.service.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 聊天请求DTO
 */
@Data
public class ChatRequest {

    /**
     * 用户输入内容
     */
    @NotBlank(message = "输入内容不能为空")
    private String query;

    /**
     * 输入参数
     */
    private Map<String, Object> inputs;

    /**
     * 响应模式：blocking（阻塞）或 streaming（流式）
     */
    private String responseMode = "blocking";

    /**
     * 对话ID（可选，用于继续对话）
     */
    private String conversationId;

    /**
     * 用户标识
     */
    @NotBlank(message = "用户标识不能为空")
    private String user;

    /**
     * 是否自动生成名称
     */
    private Boolean autoGenerateName = true;

    /**
     * 文件列表（可选）
     */
    private FileInfo[] files;

    /**
     * 文件信息
     */
    @Data
    public static class FileInfo {
        /**
         * 文件类型
         */
        private String type;

        /**
         * 传输方法
         */
        private String transferMethod;

        /**
         * 文件URL
         */
        private String url;

        /**
         * 上传文件ID
         */
        private String uploadFileId;
    }
}
