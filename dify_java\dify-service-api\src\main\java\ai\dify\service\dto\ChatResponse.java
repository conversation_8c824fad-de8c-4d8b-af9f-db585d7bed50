package ai.dify.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天响应DTO
 */
@Data
public class ChatResponse {

    /**
     * 事件类型
     */
    private String event;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 消息ID
     */
    private String id;

    /**
     * 消息ID（兼容字段）
     */
    private String messageId;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 模式
     */
    private String mode;

    /**
     * 回答内容
     */
    private String answer;

    /**
     * 元数据
     */
    private Metadata metadata;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 元数据信息
     */
    @Data
    public static class Metadata {
        /**
         * 使用情况
         */
        private Usage usage;

        /**
         * 检索资源
         */
        private List<RetrievalResource> retrieverResources;

        /**
         * 使用情况统计
         */
        @Data
        public static class Usage {
            /**
             * 提示令牌数
             */
            private Integer promptTokens;

            /**
             * 提示单价
             */
            private String promptUnitPrice;

            /**
             * 提示价格单位
             */
            private String promptPriceUnit;

            /**
             * 完成令牌数
             */
            private Integer completionTokens;

            /**
             * 完成单价
             */
            private String completionUnitPrice;

            /**
             * 完成价格单位
             */
            private String completionPriceUnit;

            /**
             * 总令牌数
             */
            private Integer totalTokens;

            /**
             * 总价格
             */
            private String totalPrice;

            /**
             * 货币
             */
            private String currency;

            /**
             * 延迟（毫秒）
             */
            private Double latency;
        }

        /**
         * 检索资源
         */
        @Data
        public static class RetrievalResource {
            /**
             * 位置
             */
            private Integer position;

            /**
             * 数据集ID
             */
            private String datasetId;

            /**
             * 数据集名称
             */
            private String datasetName;

            /**
             * 文档ID
             */
            private String documentId;

            /**
             * 文档名称
             */
            private String documentName;

            /**
             * 数据源类型
             */
            private String dataSourceType;

            /**
             * 分段ID
             */
            private String segmentId;

            /**
             * 分段位置
             */
            private Integer segmentPosition;

            /**
             * 分段词数
             */
            private Integer segmentWordCount;

            /**
             * 分段内容
             */
            private String segmentContent;

            /**
             * 相似度分数
             */
            private Double score;

            /**
             * 命中次数
             */
            private Integer hitCount;
        }
    }
}
