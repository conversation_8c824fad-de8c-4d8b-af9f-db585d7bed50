package ai.dify.service.service;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.domain.entity.App;
import ai.dify.domain.repository.AppRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 认证服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final AppRepository appRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String API_KEY_PREFIX = "Bearer ";
    private static final String API_KEY_CACHE_PREFIX = "api_key:";
    private static final long API_KEY_CACHE_TTL = 300; // 5分钟

    /**
     * 验证API密钥
     */
    public void validateApiKey(String authorization) {
        if (authorization == null || !authorization.startsWith(API_KEY_PREFIX)) {
            throw new DifyException(ResultCode.UNAUTHORIZED, "无效的API密钥格式");
        }
        
        String apiKey = authorization.substring(API_KEY_PREFIX.length());
        if (apiKey.trim().isEmpty()) {
            throw new DifyException(ResultCode.UNAUTHORIZED, "API密钥不能为空");
        }
        
        // 检查API密钥是否有效
        if (!isValidApiKey(apiKey)) {
            throw new DifyException(ResultCode.UNAUTHORIZED, "无效的API密钥");
        }
    }

    /**
     * 验证API密钥并获取应用
     */
    public App validateApiKeyAndGetApp(String authorization) {
        if (authorization == null || !authorization.startsWith(API_KEY_PREFIX)) {
            throw new DifyException(ResultCode.UNAUTHORIZED, "无效的API密钥格式");
        }
        
        String apiKey = authorization.substring(API_KEY_PREFIX.length());
        if (apiKey.trim().isEmpty()) {
            throw new DifyException(ResultCode.UNAUTHORIZED, "API密钥不能为空");
        }
        
        // 从缓存获取应用
        App app = getAppFromCache(apiKey);
        if (app != null) {
            return app;
        }
        
        // 从数据库查询应用
        app = appRepository.findByApiKey(apiKey);
        if (app == null) {
            throw new DifyException(ResultCode.UNAUTHORIZED, "无效的API密钥");
        }
        
        // 检查应用状态
        if (!"published".equals(app.getStatus().name().toLowerCase())) {
            throw new DifyException(ResultCode.FORBIDDEN, "应用未发布");
        }
        
        if (!app.getEnableApi()) {
            throw new DifyException(ResultCode.FORBIDDEN, "应用API未启用");
        }
        
        // 缓存应用信息
        cacheApp(apiKey, app);
        
        return app;
    }

    /**
     * 检查API密钥是否有效
     */
    private boolean isValidApiKey(String apiKey) {
        try {
            // 先检查缓存
            String cacheKey = API_KEY_CACHE_PREFIX + apiKey;
            Boolean cached = (Boolean) redisTemplate.opsForValue().get(cacheKey);
            if (cached != null) {
                return cached;
            }
            
            // 从数据库验证
            App app = appRepository.findByApiKey(apiKey);
            boolean isValid = app != null && 
                             "published".equals(app.getStatus().name().toLowerCase()) && 
                             app.getEnableApi();
            
            // 缓存结果
            redisTemplate.opsForValue().set(cacheKey, isValid, API_KEY_CACHE_TTL, TimeUnit.SECONDS);
            
            return isValid;
            
        } catch (Exception e) {
            log.error("验证API密钥失败: apiKey={}", apiKey, e);
            return false;
        }
    }

    /**
     * 从缓存获取应用
     */
    private App getAppFromCache(String apiKey) {
        try {
            String cacheKey = API_KEY_CACHE_PREFIX + "app:" + apiKey;
            return (App) redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("从缓存获取应用失败: apiKey={}", apiKey, e);
            return null;
        }
    }

    /**
     * 缓存应用信息
     */
    private void cacheApp(String apiKey, App app) {
        try {
            String cacheKey = API_KEY_CACHE_PREFIX + "app:" + apiKey;
            redisTemplate.opsForValue().set(cacheKey, app, API_KEY_CACHE_TTL, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("缓存应用信息失败: apiKey={}", apiKey, e);
        }
    }

    /**
     * 清除API密钥缓存
     */
    public void clearApiKeyCache(String apiKey) {
        try {
            String validationCacheKey = API_KEY_CACHE_PREFIX + apiKey;
            String appCacheKey = API_KEY_CACHE_PREFIX + "app:" + apiKey;
            
            redisTemplate.delete(validationCacheKey);
            redisTemplate.delete(appCacheKey);
            
            log.debug("清除API密钥缓存: apiKey={}", apiKey);
            
        } catch (Exception e) {
            log.warn("清除API密钥缓存失败: apiKey={}", apiKey, e);
        }
    }

    /**
     * 生成API密钥
     */
    public String generateApiKey() {
        // 生成格式: app-{timestamp}-{random}
        long timestamp = System.currentTimeMillis();
        String random = java.util.UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("app-%d-%s", timestamp, random);
    }

    /**
     * 验证用户权限
     */
    public void validateUserPermission(String userId, String resourceId, String permission) {
        // TODO: 实现用户权限验证逻辑
        log.debug("验证用户权限: userId={}, resourceId={}, permission={}", userId, resourceId, permission);
    }

    /**
     * 检查API调用限制
     */
    public void checkApiRateLimit(String apiKey, String endpoint) {
        try {
            App app = validateApiKeyAndGetApp("Bearer " + apiKey);
            
            if (app.getApiRpmEnabled() && app.getApiRpm() != null) {
                String rateLimitKey = String.format("rate_limit:%s:%s", apiKey, endpoint);
                String currentMinute = String.valueOf(System.currentTimeMillis() / 60000);
                String key = rateLimitKey + ":" + currentMinute;
                
                Long currentCount = redisTemplate.opsForValue().increment(key);
                if (currentCount == 1) {
                    redisTemplate.expire(key, 60, TimeUnit.SECONDS);
                }
                
                if (currentCount > app.getApiRpm()) {
                    throw new DifyException(ResultCode.RATE_LIMIT_EXCEEDED, "API调用频率超限");
                }
            }
            
        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("检查API限制失败", e);
            // 不抛出异常，避免影响正常请求
        }
    }
}
