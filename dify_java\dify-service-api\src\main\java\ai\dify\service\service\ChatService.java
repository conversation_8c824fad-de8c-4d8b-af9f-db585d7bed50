package ai.dify.service.service;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.domain.entity.App;
import ai.dify.domain.entity.Conversation;
import ai.dify.domain.entity.Message;
import ai.dify.domain.repository.AppRepository;
import ai.dify.domain.repository.ConversationRepository;
import ai.dify.domain.repository.MessageRepository;
import ai.dify.model.runtime.ModelRuntime;
import ai.dify.model.runtime.model.ChatMessage;
import ai.dify.model.runtime.model.ModelRequest;
import ai.dify.model.runtime.model.ModelResponse;
import ai.dify.service.dto.ChatRequest;
import ai.dify.service.dto.ChatResponse;
import ai.dify.service.dto.ConversationResponse;
import ai.dify.service.dto.MessageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 聊天服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatService {

    private final AppRepository appRepository;
    private final ConversationRepository conversationRepository;
    private final MessageRepository messageRepository;
    private final ModelRuntime modelRuntime;
    private final AuthService authService;

    /**
     * 发送聊天消息
     */
    @Transactional
    public ChatResponse chatMessage(String authorization, ChatRequest request) {
        log.debug("处理聊天请求: user={}, query={}", request.getUser(), request.getQuery());
        
        try {
            // 验证API密钥并获取应用
            App app = authService.validateApiKeyAndGetApp(authorization);
            
            // 获取或创建对话
            Conversation conversation = getOrCreateConversation(app, request);
            
            // 获取对话历史
            List<ChatMessage> history = getConversationHistory(conversation.getId());
            
            // 添加用户消息
            history.add(ChatMessage.user(request.getQuery()));
            
            // 调用模型
            ModelRequest modelRequest = buildModelRequest(app, history, request);
            ModelResponse modelResponse = modelRuntime.invoke(modelRequest);
            
            if (!modelResponse.isSuccess()) {
                throw new DifyException(ResultCode.MODEL_REQUEST_ERROR, "模型调用失败: " + modelResponse.getError());
            }
            
            // 保存用户消息
            Message userMessage = saveUserMessage(conversation, request);
            
            // 保存AI回复
            Message aiMessage = saveAiMessage(conversation, modelResponse, userMessage);
            
            // 构建响应
            ChatResponse response = buildChatResponse(aiMessage, conversation, modelResponse);
            
            log.debug("聊天处理完成: messageId={}", aiMessage.getId());
            return response;
            
        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("聊天处理失败", e);
            throw new DifyException(ResultCode.ERROR, "聊天处理失败: " + e.getMessage());
        }
    }

    /**
     * 流式聊天消息
     */
    public Flux<String> chatMessageStream(String authorization, ChatRequest request) {
        log.debug("处理流式聊天请求: user={}, query={}", request.getUser(), request.getQuery());
        
        return Flux.create(sink -> {
            try {
                // 验证API密钥并获取应用
                App app = authService.validateApiKeyAndGetApp(authorization);
                
                // 获取或创建对话
                Conversation conversation = getOrCreateConversation(app, request);
                
                // 获取对话历史
                List<ChatMessage> history = getConversationHistory(conversation.getId());
                history.add(ChatMessage.user(request.getQuery()));
                
                // 保存用户消息
                Message userMessage = saveUserMessage(conversation, request);
                
                String messageId = UUID.randomUUID().toString();
                String conversationId = conversation.getId();
                
                // 构建模型请求
                ModelRequest modelRequest = buildModelRequest(app, history, request);
                
                // 流式调用模型
                modelRuntime.invokeStream(modelRequest)
                    .doOnNext(chunk -> {
                        String eventData = String.format(
                            "data: {\"event\":\"message\",\"message_id\":\"%s\",\"conversation_id\":\"%s\",\"answer\":\"%s\"}\n\n",
                            messageId, conversationId, escapeJson(chunk.getContent())
                        );
                        sink.next(eventData);
                    })
                    .doOnComplete(() -> {
                        String endEvent = String.format(
                            "data: {\"event\":\"message_end\",\"message_id\":\"%s\",\"conversation_id\":\"%s\"}\n\n",
                            messageId, conversationId
                        );
                        sink.next(endEvent);
                        sink.complete();
                    })
                    .doOnError(error -> {
                        log.error("流式聊天失败", error);
                        String errorEvent = String.format(
                            "data: {\"event\":\"error\",\"message_id\":\"%s\",\"error\":\"%s\"}\n\n",
                            messageId, escapeJson(error.getMessage())
                        );
                        sink.next(errorEvent);
                        sink.error(error);
                    })
                    .subscribe();
                    
            } catch (Exception e) {
                log.error("流式聊天初始化失败", e);
                sink.error(e);
            }
        });
    }

    /**
     * 获取消息历史
     */
    public List<MessageResponse> getMessages(String authorization, String conversationId, 
                                           String user, String firstId, Integer limit) {
        log.debug("获取消息历史: conversationId={}, user={}", conversationId, user);
        
        try {
            // 验证API密钥
            authService.validateApiKey(authorization);
            
            // 查询消息
            List<Message> messages = messageRepository.findByConversationId(conversationId, firstId, limit);
            
            return messages.stream()
                    .map(this::convertToMessageResponse)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取消息历史失败", e);
            throw new DifyException(ResultCode.ERROR, "获取消息历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取对话列表
     */
    public List<ConversationResponse> getConversations(String authorization, String user, 
                                                     String firstId, Integer limit, Boolean pinned) {
        log.debug("获取对话列表: user={}, pinned={}", user, pinned);
        
        try {
            // 验证API密钥并获取应用
            App app = authService.validateApiKeyAndGetApp(authorization);
            
            // 查询对话
            List<Conversation> conversations = conversationRepository.findByAppIdAndUser(
                    app.getId(), user, firstId, limit, pinned);
            
            return conversations.stream()
                    .map(this::convertToConversationResponse)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取对话列表失败", e);
            throw new DifyException(ResultCode.ERROR, "获取对话列表失败: " + e.getMessage());
        }
    }

    /**
     * 重命名对话
     */
    @Transactional
    public void renameConversation(String authorization, String conversationId, String user, String name) {
        log.debug("重命名对话: conversationId={}, name={}", conversationId, name);
        
        try {
            // 验证API密钥
            authService.validateApiKey(authorization);
            
            // 查找对话
            Conversation conversation = conversationRepository.selectById(conversationId);
            if (conversation == null) {
                throw new DifyException(ResultCode.NOT_FOUND, "对话不存在");
            }
            
            // 验证用户权限
            if (!user.equals(conversation.getFromEndUserId())) {
                throw new DifyException(ResultCode.FORBIDDEN, "无权限操作此对话");
            }
            
            // 更新对话名称
            conversation.setName(name);
            conversation.setUpdatedAt(LocalDateTime.now());
            conversationRepository.updateById(conversation);
            
            log.debug("对话重命名成功: conversationId={}", conversationId);
            
        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("重命名对话失败", e);
            throw new DifyException(ResultCode.ERROR, "重命名对话失败: " + e.getMessage());
        }
    }

    /**
     * 获取或创建对话
     */
    private Conversation getOrCreateConversation(App app, ChatRequest request) {
        if (request.getConversationId() != null && !request.getConversationId().trim().isEmpty()) {
            // 获取现有对话
            Conversation conversation = conversationRepository.selectById(request.getConversationId());
            if (conversation != null && app.getId().equals(conversation.getAppId())) {
                return conversation;
            }
        }
        
        // 创建新对话
        Conversation conversation = new Conversation();
        conversation.setId(UUID.randomUUID().toString());
        conversation.setAppId(app.getId());
        conversation.setFromEndUserId(request.getUser());
        conversation.setName("新对话");
        conversation.setCreatedAt(LocalDateTime.now());
        conversation.setUpdatedAt(LocalDateTime.now());
        
        conversationRepository.insert(conversation);
        return conversation;
    }

    /**
     * 获取对话历史
     */
    private List<ChatMessage> getConversationHistory(String conversationId) {
        List<Message> messages = messageRepository.findByConversationIdOrderByCreatedAt(conversationId);
        List<ChatMessage> history = new ArrayList<>();
        
        for (Message message : messages) {
            if ("user".equals(message.getFromSource())) {
                history.add(ChatMessage.user(message.getQuery()));
            } else if ("assistant".equals(message.getFromSource())) {
                history.add(ChatMessage.assistant(message.getAnswer()));
            }
        }
        
        return history;
    }

    /**
     * 构建模型请求
     */
    private ModelRequest buildModelRequest(App app, List<ChatMessage> history, ChatRequest request) {
        // TODO: 根据应用配置构建模型请求
        ModelRequest modelRequest = new ModelRequest();
        modelRequest.setProvider("openai");
        modelRequest.setModel("gpt-3.5-turbo");
        modelRequest.setMessages(history);
        modelRequest.setTemperature(0.7);
        modelRequest.setMaxTokens(1000);
        
        return modelRequest;
    }

    /**
     * 保存用户消息
     */
    private Message saveUserMessage(Conversation conversation, ChatRequest request) {
        Message message = new Message();
        message.setId(UUID.randomUUID().toString());
        message.setAppId(conversation.getAppId());
        message.setConversationId(conversation.getId());
        message.setQuery(request.getQuery());
        message.setFromSource("user");
        message.setFromEndUserId(request.getUser());
        message.setCreatedAt(LocalDateTime.now());
        
        messageRepository.insert(message);
        return message;
    }

    /**
     * 保存AI回复
     */
    private Message saveAiMessage(Conversation conversation, ModelResponse modelResponse, Message userMessage) {
        Message message = new Message();
        message.setId(UUID.randomUUID().toString());
        message.setAppId(conversation.getAppId());
        message.setConversationId(conversation.getId());
        message.setAnswer(modelResponse.getContent());
        message.setFromSource("assistant");
        message.setParentMessageId(userMessage.getId());
        message.setCreatedAt(LocalDateTime.now());
        
        messageRepository.insert(message);
        return message;
    }

    /**
     * 构建聊天响应
     */
    private ChatResponse buildChatResponse(Message message, Conversation conversation, ModelResponse modelResponse) {
        ChatResponse response = new ChatResponse();
        response.setEvent("message");
        response.setId(message.getId());
        response.setConversationId(conversation.getId());
        response.setAnswer(message.getAnswer());
        response.setMode("chat");
        response.setCreatedAt(message.getCreatedAt());
        
        return response;
    }

    /**
     * 转换为消息响应
     */
    private MessageResponse convertToMessageResponse(Message message) {
        MessageResponse response = new MessageResponse();
        response.setId(message.getId());
        response.setConversationId(message.getConversationId());
        response.setQuery(message.getQuery());
        response.setAnswer(message.getAnswer());
        response.setCreatedAt(message.getCreatedAt());
        
        return response;
    }

    /**
     * 转换为对话响应
     */
    private ConversationResponse convertToConversationResponse(Conversation conversation) {
        ConversationResponse response = new ConversationResponse();
        response.setId(conversation.getId());
        response.setName(conversation.getName());
        response.setCreatedAt(conversation.getCreatedAt());
        response.setUpdatedAt(conversation.getUpdatedAt());
        
        return response;
    }

    /**
     * 转义JSON字符串
     */
    private String escapeJson(String text) {
        if (text == null) return "";
        return text.replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
