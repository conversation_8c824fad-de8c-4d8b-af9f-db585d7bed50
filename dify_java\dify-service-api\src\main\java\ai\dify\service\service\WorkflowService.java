package ai.dify.service.service;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.domain.entity.App;
import ai.dify.domain.entity.Workflow;
import ai.dify.domain.repository.WorkflowRepository;
import ai.dify.service.dto.WorkflowExecuteRequest;
import ai.dify.service.dto.WorkflowExecuteResponse;
import ai.dify.workflow.engine.WorkflowEngine;
import ai.dify.workflow.engine.WorkflowDefinition;
import ai.dify.workflow.engine.WorkflowResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 工作流服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowService {

    private final WorkflowRepository workflowRepository;
    private final WorkflowEngine workflowEngine;
    private final AuthService authService;

    /**
     * 执行工作流
     */
    public WorkflowExecuteResponse executeWorkflow(String authorization, WorkflowExecuteRequest request) {
        log.debug("执行工作流: user={}, inputs={}", request.getUser(), request.getInputs());
        
        try {
            // 验证API密钥并获取应用
            App app = authService.validateApiKeyAndGetApp(authorization);
            
            // 获取工作流定义
            WorkflowDefinition workflowDefinition = getWorkflowDefinition(app);
            
            // 执行工作流
            WorkflowResult result = workflowEngine.executeWorkflow(workflowDefinition, request.getInputs()).get();
            
            // 构建响应
            WorkflowExecuteResponse response = buildWorkflowResponse(result, request);
            
            log.debug("工作流执行完成: executionId={}, status={}", result.getExecutionId(), result.getStatus());
            return response;
            
        } catch (DifyException e) {
            throw e;
        } catch (Exception e) {
            log.error("工作流执行失败", e);
            throw new DifyException(ResultCode.WORKFLOW_EXECUTION_ERROR, "工作流执行失败: " + e.getMessage());
        }
    }

    /**
     * 流式执行工作流
     */
    public Flux<String> executeWorkflowStream(String authorization, WorkflowExecuteRequest request) {
        log.debug("流式执行工作流: user={}, inputs={}", request.getUser(), request.getInputs());
        
        return Flux.create(sink -> {
            try {
                // 验证API密钥并获取应用
                App app = authService.validateApiKeyAndGetApp(authorization);
                
                // 获取工作流定义
                WorkflowDefinition workflowDefinition = getWorkflowDefinition(app);
                
                String executionId = UUID.randomUUID().toString();
                
                // 发送开始事件
                String startEvent = String.format(
                    "data: {\"event\":\"workflow_started\",\"execution_id\":\"%s\"}\n\n",
                    executionId
                );
                sink.next(startEvent);
                
                // 异步执行工作流
                CompletableFuture.supplyAsync(() -> {
                    try {
                        return workflowEngine.executeWorkflow(workflowDefinition, request.getInputs()).get();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }).whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("工作流执行失败", throwable);
                        String errorEvent = String.format(
                            "data: {\"event\":\"workflow_failed\",\"execution_id\":\"%s\",\"error\":\"%s\"}\n\n",
                            executionId, escapeJson(throwable.getMessage())
                        );
                        sink.next(errorEvent);
                        sink.error(throwable);
                    } else {
                        // 发送完成事件
                        String completeEvent = String.format(
                            "data: {\"event\":\"workflow_finished\",\"execution_id\":\"%s\",\"outputs\":%s}\n\n",
                            executionId, convertOutputsToJson(result.getOutputs())
                        );
                        sink.next(completeEvent);
                        sink.complete();
                    }
                });
                
            } catch (Exception e) {
                log.error("工作流流式执行初始化失败", e);
                sink.error(e);
            }
        });
    }

    /**
     * 停止工作流执行
     */
    public void stopWorkflowExecution(String authorization, String taskId) {
        log.debug("停止工作流执行: taskId={}", taskId);
        
        try {
            // 验证API密钥
            authService.validateApiKey(authorization);
            
            // 停止工作流
            workflowEngine.stopWorkflow(taskId);
            
            log.debug("工作流停止成功: taskId={}", taskId);
            
        } catch (Exception e) {
            log.error("停止工作流失败: taskId={}", taskId, e);
            throw new DifyException(ResultCode.ERROR, "停止工作流失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流执行状态
     */
    public Map<String, Object> getWorkflowExecutionStatus(String authorization, String executionId) {
        log.debug("获取工作流执行状态: executionId={}", executionId);
        
        try {
            // 验证API密钥
            authService.validateApiKey(authorization);
            
            // 获取执行状态
            var status = workflowEngine.getExecutionStatus(executionId);
            
            return Map.of(
                "execution_id", executionId,
                "status", status.name(),
                "timestamp", LocalDateTime.now()
            );
            
        } catch (Exception e) {
            log.error("获取工作流执行状态失败: executionId={}", executionId, e);
            throw new DifyException(ResultCode.ERROR, "获取执行状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流定义
     */
    private WorkflowDefinition getWorkflowDefinition(App app) {
        // 查找应用关联的工作流
        Workflow workflow = workflowRepository.findByAppIdAndPublished(app.getId(), true);
        if (workflow == null) {
            throw new DifyException(ResultCode.NOT_FOUND, "应用未配置工作流");
        }
        
        // 转换为工作流定义
        return convertToWorkflowDefinition(workflow);
    }

    /**
     * 转换为工作流定义
     */
    private WorkflowDefinition convertToWorkflowDefinition(Workflow workflow) {
        WorkflowDefinition definition = new WorkflowDefinition();
        definition.setId(workflow.getId());
        definition.setName("App Workflow");
        definition.setVersion(workflow.getVersion());
        
        // TODO: 解析工作流图结构
        // 这里需要将数据库中的JSON图结构转换为WorkflowDefinition对象
        
        return definition;
    }

    /**
     * 构建工作流响应
     */
    private WorkflowExecuteResponse buildWorkflowResponse(WorkflowResult result, WorkflowExecuteRequest request) {
        WorkflowExecuteResponse response = new WorkflowExecuteResponse();
        response.setWorkflowRunId(result.getExecutionId());
        response.setTaskId(result.getExecutionId());
        response.setData(result.getOutputs());
        response.setCreatedAt(LocalDateTime.now());
        
        return response;
    }

    /**
     * 转换输出为JSON字符串
     */
    private String convertOutputsToJson(Map<String, Object> outputs) {
        try {
            // 简化实现，实际应该使用Jackson
            if (outputs == null || outputs.isEmpty()) {
                return "{}";
            }
            
            StringBuilder json = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : outputs.entrySet()) {
                if (!first) {
                    json.append(",");
                }
                json.append("\"").append(entry.getKey()).append("\":\"")
                    .append(escapeJson(String.valueOf(entry.getValue()))).append("\"");
                first = false;
            }
            json.append("}");
            
            return json.toString();
            
        } catch (Exception e) {
            log.warn("转换输出为JSON失败", e);
            return "{}";
        }
    }

    /**
     * 转义JSON字符串
     */
    private String escapeJson(String text) {
        if (text == null) return "";
        return text.replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
