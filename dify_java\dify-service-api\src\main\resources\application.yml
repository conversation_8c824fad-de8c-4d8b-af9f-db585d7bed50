server:
  port: 5002

spring:
  application:
    name: dify-service-api
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *************************************
    username: postgres
    password: difyai123456
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  redis:
    host: localhost
    port: 6379
    password: difyai123456
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_uuid
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*.xml

# 日志配置
logging:
  level:
    ai.dify: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/dify-service-api.log

# Swagger配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: ai.dify.service.controller

# 应用配置
dify:
  # API配置
  api:
    rate-limit:
      enabled: true
      requests-per-minute: 60
      requests-per-hour: 1000
    auth:
      required: true
      header-name: Authorization
      prefix: Bearer
  
  # 模型配置
  model:
    default-provider: openai
    providers:
      openai:
        api-key: ${OPENAI_API_KEY:}
        base-url: https://api.openai.com/v1
        timeout: 60000
      anthropic:
        api-key: ${ANTHROPIC_API_KEY:}
        base-url: https://api.anthropic.com
        timeout: 60000
  
  # 工作流配置
  workflow:
    max-execution-time: 300000 # 5分钟
    max-nodes: 100
    enable-debug: true
  
  # RAG配置
  rag:
    default-top-k: 5
    default-score-threshold: 0.5
    max-segment-length: 2000
    segment-overlap: 200

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: *****************************************
  
  redis:
    database: 1

dify:
  api:
    rate-limit:
      enabled: false

logging:
  level:
    root: INFO
    ai.dify: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:dify}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:difyai123456}
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:difyai123456}
    database: ${REDIS_DB:0}

logging:
  level:
    root: WARN
    ai.dify: INFO
  file:
    name: /var/log/dify/service-api.log
