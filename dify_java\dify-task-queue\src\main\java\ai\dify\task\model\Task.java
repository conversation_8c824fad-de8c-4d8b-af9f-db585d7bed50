package ai.dify.task.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务模型
 */
@Data
public class Task implements Serializable {

    /**
     * 任务ID
     */
    private String id;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务参数
     */
    private Map<String, Object> parameters;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 优先级（1-10，数字越大优先级越高）
     */
    private Integer priority = 5;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 超时时间（秒）
     */
    private Integer timeoutSeconds = 1800; // 30分钟

    /**
     * 提交时间
     */
    private LocalDateTime submittedAt;

    /**
     * 开始时间
     */
    private LocalDateTime startedAt;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 提交用户
     */
    private String submittedBy;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务标签
     */
    private Map<String, String> tags;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        /**
         * 等待中
         */
        PENDING,

        /**
         * 运行中
         */
        RUNNING,

        /**
         * 成功
         */
        SUCCESS,

        /**
         * 失败
         */
        FAILED,

        /**
         * 已取消
         */
        CANCELLED,

        /**
         * 超时
         */
        TIMEOUT
    }

    /**
     * 任务类型常量
     */
    public static class TaskType {
        public static final String DATASET_INDEXING = "dataset_indexing";
        public static final String DOCUMENT_PROCESSING = "document_processing";
        public static final String MODEL_TRAINING = "model_training";
        public static final String WORKFLOW_EXECUTION = "workflow_execution";
        public static final String EMAIL_SENDING = "email_sending";
        public static final String FILE_PROCESSING = "file_processing";
        public static final String DATA_EXPORT = "data_export";
        public static final String SYSTEM_MAINTENANCE = "system_maintenance";
        public static final String BATCH_OPERATION = "batch_operation";
        public static final String REPORT_GENERATION = "report_generation";
    }

    /**
     * 创建任务
     */
    public static Task create(String type, Map<String, Object> parameters) {
        Task task = new Task();
        task.setType(type);
        task.setParameters(parameters);
        task.setStatus(TaskStatus.PENDING);
        task.setSubmittedAt(LocalDateTime.now());
        return task;
    }

    /**
     * 创建任务（带描述）
     */
    public static Task create(String type, Map<String, Object> parameters, String description) {
        Task task = create(type, parameters);
        task.setDescription(description);
        return task;
    }

    /**
     * 创建高优先级任务
     */
    public static Task createHighPriority(String type, Map<String, Object> parameters) {
        Task task = create(type, parameters);
        task.setPriority(8);
        return task;
    }

    /**
     * 创建低优先级任务
     */
    public static Task createLowPriority(String type, Map<String, Object> parameters) {
        Task task = create(type, parameters);
        task.setPriority(2);
        return task;
    }

    /**
     * 检查任务是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetries && 
               (status == TaskStatus.FAILED || status == TaskStatus.TIMEOUT);
    }

    /**
     * 检查任务是否已完成
     */
    public boolean isCompleted() {
        return status == TaskStatus.SUCCESS || 
               status == TaskStatus.FAILED || 
               status == TaskStatus.CANCELLED ||
               status == TaskStatus.TIMEOUT;
    }

    /**
     * 检查任务是否成功
     */
    public boolean isSuccess() {
        return status == TaskStatus.SUCCESS;
    }

    /**
     * 获取执行时长（毫秒）
     */
    public Long getExecutionDuration() {
        if (startedAt != null && completedAt != null) {
            return java.time.Duration.between(startedAt, completedAt).toMillis();
        }
        return null;
    }

    /**
     * 获取等待时长（毫秒）
     */
    public Long getWaitingDuration() {
        if (submittedAt != null && startedAt != null) {
            return java.time.Duration.between(submittedAt, startedAt).toMillis();
        }
        return null;
    }

    /**
     * 添加标签
     */
    public void addTag(String key, String value) {
        if (tags == null) {
            tags = new java.util.HashMap<>();
        }
        tags.put(key, value);
    }

    /**
     * 获取标签
     */
    public String getTag(String key) {
        return tags != null ? tags.get(key) : null;
    }

    /**
     * 获取参数
     */
    public Object getParameter(String key) {
        return parameters != null ? parameters.get(key) : null;
    }

    /**
     * 设置参数
     */
    public void setParameter(String key, Object value) {
        if (parameters == null) {
            parameters = new java.util.HashMap<>();
        }
        parameters.put(key, value);
    }
}
