package ai.dify.task.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务结果
 */
@Data
public class TaskResult implements Serializable {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 输出结果
     */
    private Object output;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 异常类型
     */
    private String exception;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 执行时长（毫秒）
     */
    private Long executionTime;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建成功结果
     */
    public static TaskResult success(Object output) {
        TaskResult result = new TaskResult();
        result.setSuccess(true);
        result.setOutput(output);
        result.setCompletedAt(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static TaskResult failure(String error) {
        TaskResult result = new TaskResult();
        result.setSuccess(false);
        result.setError(error);
        result.setCompletedAt(LocalDateTime.now());
        return result;
    }

    /**
     * 创建异常结果
     */
    public static TaskResult exception(Exception e) {
        TaskResult result = new TaskResult();
        result.setSuccess(false);
        result.setError(e.getMessage());
        result.setException(e.getClass().getSimpleName());
        result.setCompletedAt(LocalDateTime.now());
        return result;
    }

    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new java.util.HashMap<>();
        }
        metadata.put(key, value);
    }

    /**
     * 获取元数据
     */
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
}
