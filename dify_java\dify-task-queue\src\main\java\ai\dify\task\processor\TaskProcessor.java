package ai.dify.task.processor;

import ai.dify.task.model.Task;

/**
 * 任务处理器接口
 */
public interface TaskProcessor {

    /**
     * 获取处理器支持的任务类型
     */
    String getTaskType();

    /**
     * 处理任务
     */
    Object process(Task task) throws Exception;

    /**
     * 验证任务参数
     */
    default boolean validateTask(Task task) {
        return task != null && getTaskType().equals(task.getType());
    }

    /**
     * 获取处理器描述
     */
    default String getDescription() {
        return "Task processor for " + getTaskType();
    }

    /**
     * 是否支持重试
     */
    default boolean supportsRetry() {
        return true;
    }

    /**
     * 获取默认超时时间（秒）
     */
    default int getDefaultTimeoutSeconds() {
        return 1800; // 30分钟
    }
}
