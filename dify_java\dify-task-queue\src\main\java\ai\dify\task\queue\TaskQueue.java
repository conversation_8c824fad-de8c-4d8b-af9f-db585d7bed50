package ai.dify.task.queue;

import ai.dify.task.model.Task;
import ai.dify.task.model.TaskResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 任务队列
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskQueue {

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String TASK_QUEUE_KEY = "dify:task:queue";
    private static final String TASK_PROCESSING_KEY = "dify:task:processing";
    private static final String TASK_RESULT_KEY = "dify:task:result:";
    private static final String TASK_STATUS_KEY = "dify:task:status:";

    /**
     * 提交任务
     */
    public String submitTask(Task task) {
        try {
            // 设置任务ID和提交时间
            if (task.getId() == null) {
                task.setId(generateTaskId());
            }
            task.setSubmittedAt(LocalDateTime.now());
            task.setStatus(Task.TaskStatus.PENDING);

            // 将任务添加到队列
            redisTemplate.opsForList().leftPush(TASK_QUEUE_KEY, task);

            // 设置任务状态
            setTaskStatus(task.getId(), task.getStatus());

            log.debug("任务已提交到队列: taskId={}, type={}", task.getId(), task.getType());
            return task.getId();

        } catch (Exception e) {
            log.error("提交任务失败", e);
            throw new RuntimeException("提交任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取下一个任务
     */
    public Task getNextTask() {
        try {
            // 从队列右侧弹出任务（FIFO）
            Object taskObj = redisTemplate.opsForList().rightPop(TASK_QUEUE_KEY);
            if (taskObj == null) {
                return null;
            }

            Task task = (Task) taskObj;
            
            // 将任务移到处理中队列
            task.setStatus(Task.TaskStatus.RUNNING);
            task.setStartedAt(LocalDateTime.now());
            
            redisTemplate.opsForHash().put(TASK_PROCESSING_KEY, task.getId(), task);
            setTaskStatus(task.getId(), task.getStatus());

            log.debug("获取到待处理任务: taskId={}, type={}", task.getId(), task.getType());
            return task;

        } catch (Exception e) {
            log.error("获取任务失败", e);
            return null;
        }
    }

    /**
     * 完成任务
     */
    public void completeTask(String taskId, TaskResult result) {
        try {
            // 从处理中队列移除
            Task task = (Task) redisTemplate.opsForHash().get(TASK_PROCESSING_KEY, taskId);
            if (task != null) {
                redisTemplate.opsForHash().delete(TASK_PROCESSING_KEY, taskId);
                
                // 更新任务状态
                task.setStatus(result.isSuccess() ? Task.TaskStatus.SUCCESS : Task.TaskStatus.FAILED);
                task.setCompletedAt(LocalDateTime.now());
                
                // 保存任务结果
                result.setTaskId(taskId);
                result.setCompletedAt(LocalDateTime.now());
                
                String resultKey = TASK_RESULT_KEY + taskId;
                redisTemplate.opsForValue().set(resultKey, result, Duration.ofHours(24));
                
                setTaskStatus(taskId, task.getStatus());
                
                log.debug("任务已完成: taskId={}, success={}", taskId, result.isSuccess());
            }

        } catch (Exception e) {
            log.error("完成任务失败: taskId={}", taskId, e);
        }
    }

    /**
     * 获取任务结果
     */
    public TaskResult getTaskResult(String taskId) {
        try {
            String resultKey = TASK_RESULT_KEY + taskId;
            return (TaskResult) redisTemplate.opsForValue().get(resultKey);
        } catch (Exception e) {
            log.error("获取任务结果失败: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 获取任务状态
     */
    public Task.TaskStatus getTaskStatus(String taskId) {
        try {
            String statusKey = TASK_STATUS_KEY + taskId;
            String status = (String) redisTemplate.opsForValue().get(statusKey);
            return status != null ? Task.TaskStatus.valueOf(status) : null;
        } catch (Exception e) {
            log.error("获取任务状态失败: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 设置任务状态
     */
    private void setTaskStatus(String taskId, Task.TaskStatus status) {
        try {
            String statusKey = TASK_STATUS_KEY + taskId;
            redisTemplate.opsForValue().set(statusKey, status.name(), Duration.ofDays(7));
        } catch (Exception e) {
            log.error("设置任务状态失败: taskId={}, status={}", taskId, status, e);
        }
    }

    /**
     * 获取队列长度
     */
    public long getQueueSize() {
        try {
            Long size = redisTemplate.opsForList().size(TASK_QUEUE_KEY);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("获取队列长度失败", e);
            return 0;
        }
    }

    /**
     * 获取处理中任务数量
     */
    public long getProcessingCount() {
        try {
            Long size = redisTemplate.opsForHash().size(TASK_PROCESSING_KEY);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("获取处理中任务数量失败", e);
            return 0;
        }
    }

    /**
     * 清理超时任务
     */
    public void cleanupTimeoutTasks() {
        try {
            // 获取所有处理中的任务
            var processingTasks = redisTemplate.opsForHash().entries(TASK_PROCESSING_KEY);
            
            LocalDateTime now = LocalDateTime.now();
            
            for (var entry : processingTasks.entrySet()) {
                String taskId = (String) entry.getKey();
                Task task = (Task) entry.getValue();
                
                // 检查任务是否超时（默认30分钟）
                if (task.getStartedAt() != null && 
                    Duration.between(task.getStartedAt(), now).toMinutes() > 30) {
                    
                    log.warn("发现超时任务，将其标记为失败: taskId={}", taskId);
                    
                    // 创建失败结果
                    TaskResult failedResult = new TaskResult();
                    failedResult.setSuccess(false);
                    failedResult.setError("任务执行超时");
                    
                    completeTask(taskId, failedResult);
                }
            }
            
        } catch (Exception e) {
            log.error("清理超时任务失败", e);
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "task_" + System.currentTimeMillis() + "_" + 
               java.util.UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 重试失败任务
     */
    public boolean retryTask(String taskId) {
        try {
            TaskResult result = getTaskResult(taskId);
            if (result != null && !result.isSuccess()) {
                // 从处理中队列获取原始任务
                Task task = (Task) redisTemplate.opsForHash().get(TASK_PROCESSING_KEY, taskId);
                if (task != null) {
                    // 重置任务状态
                    task.setStatus(Task.TaskStatus.PENDING);
                    task.setStartedAt(null);
                    task.setCompletedAt(null);
                    task.setRetryCount(task.getRetryCount() + 1);
                    
                    // 重新提交到队列
                    redisTemplate.opsForList().leftPush(TASK_QUEUE_KEY, task);
                    redisTemplate.opsForHash().delete(TASK_PROCESSING_KEY, taskId);
                    
                    // 删除旧的结果
                    redisTemplate.delete(TASK_RESULT_KEY + taskId);
                    setTaskStatus(taskId, task.getStatus());
                    
                    log.debug("任务已重新提交: taskId={}, retryCount={}", taskId, task.getRetryCount());
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}", taskId, e);
            return false;
        }
    }
}
