package ai.dify.task.worker;

import ai.dify.task.model.Task;
import ai.dify.task.model.TaskResult;
import ai.dify.task.processor.TaskProcessor;
import ai.dify.task.queue.TaskQueue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 任务工作器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskWorker {

    private final TaskQueue taskQueue;
    private final Map<String, TaskProcessor> taskProcessors;

    @Value("${dify.task.worker.threads:5}")
    private int workerThreads;

    @Value("${dify.task.worker.enabled:true}")
    private boolean workerEnabled;

    private ExecutorService executorService;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final Map<String, Future<?>> runningTasks = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (workerEnabled) {
            executorService = Executors.newFixedThreadPool(workerThreads);
            running.set(true);
            
            // 启动工作线程
            for (int i = 0; i < workerThreads; i++) {
                final int workerId = i;
                executorService.submit(() -> workerLoop(workerId));
            }
            
            log.info("任务工作器已启动，工作线程数: {}", workerThreads);
        }
    }

    @PreDestroy
    public void destroy() {
        running.set(false);
        
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, java.util.concurrent.TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("任务工作器已停止");
    }

    /**
     * 工作循环
     */
    private void workerLoop(int workerId) {
        log.debug("工作线程 {} 已启动", workerId);
        
        while (running.get()) {
            try {
                // 获取下一个任务
                Task task = taskQueue.getNextTask();
                if (task == null) {
                    // 没有任务，休眠一段时间
                    Thread.sleep(1000);
                    continue;
                }

                log.debug("工作线程 {} 开始处理任务: {}", workerId, task.getId());
                
                // 处理任务
                processTask(task);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("工作线程 {} 处理任务时发生异常", workerId, e);
            }
        }
        
        log.debug("工作线程 {} 已停止", workerId);
    }

    /**
     * 处理任务
     */
    private void processTask(Task task) {
        TaskResult result = new TaskResult();
        result.setTaskId(task.getId());
        
        try {
            // 获取任务处理器
            TaskProcessor processor = taskProcessors.get(task.getType());
            if (processor == null) {
                result.setSuccess(false);
                result.setError("未找到任务处理器: " + task.getType());
                taskQueue.completeTask(task.getId(), result);
                return;
            }

            // 执行任务
            log.debug("开始执行任务: taskId={}, type={}", task.getId(), task.getType());
            
            Object output = processor.process(task);
            
            result.setSuccess(true);
            result.setOutput(output);
            
            log.debug("任务执行成功: taskId={}", task.getId());
            
        } catch (Exception e) {
            log.error("任务执行失败: taskId={}", task.getId(), e);
            
            result.setSuccess(false);
            result.setError(e.getMessage());
            result.setException(e.getClass().getSimpleName());
        } finally {
            // 完成任务
            taskQueue.completeTask(task.getId(), result);
        }
    }

    /**
     * 异步处理任务
     */
    @Async
    public Future<TaskResult> processTaskAsync(Task task) {
        return java.util.concurrent.CompletableFuture.supplyAsync(() -> {
            TaskResult result = new TaskResult();
            result.setTaskId(task.getId());
            
            try {
                TaskProcessor processor = taskProcessors.get(task.getType());
                if (processor == null) {
                    result.setSuccess(false);
                    result.setError("未找到任务处理器: " + task.getType());
                    return result;
                }

                Object output = processor.process(task);
                result.setSuccess(true);
                result.setOutput(output);
                
            } catch (Exception e) {
                log.error("异步任务执行失败: taskId={}", task.getId(), e);
                result.setSuccess(false);
                result.setError(e.getMessage());
            }
            
            return result;
        });
    }

    /**
     * 定时清理超时任务
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanupTimeoutTasks() {
        if (workerEnabled) {
            try {
                taskQueue.cleanupTimeoutTasks();
            } catch (Exception e) {
                log.error("清理超时任务失败", e);
            }
        }
    }

    /**
     * 获取工作器状态
     */
    public WorkerStatus getStatus() {
        WorkerStatus status = new WorkerStatus();
        status.setEnabled(workerEnabled);
        status.setRunning(running.get());
        status.setWorkerThreads(workerThreads);
        status.setQueueSize(taskQueue.getQueueSize());
        status.setProcessingCount(taskQueue.getProcessingCount());
        status.setRunningTasksCount(runningTasks.size());
        
        return status;
    }

    /**
     * 工作器状态
     */
    public static class WorkerStatus {
        private boolean enabled;
        private boolean running;
        private int workerThreads;
        private long queueSize;
        private long processingCount;
        private int runningTasksCount;

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public boolean isRunning() { return running; }
        public void setRunning(boolean running) { this.running = running; }
        
        public int getWorkerThreads() { return workerThreads; }
        public void setWorkerThreads(int workerThreads) { this.workerThreads = workerThreads; }
        
        public long getQueueSize() { return queueSize; }
        public void setQueueSize(long queueSize) { this.queueSize = queueSize; }
        
        public long getProcessingCount() { return processingCount; }
        public void setProcessingCount(long processingCount) { this.processingCount = processingCount; }
        
        public int getRunningTasksCount() { return runningTasksCount; }
        public void setRunningTasksCount(int runningTasksCount) { this.runningTasksCount = runningTasksCount; }
    }
}
