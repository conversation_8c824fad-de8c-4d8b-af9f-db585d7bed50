package ai.dify.tenant.config;

import ai.dify.tenant.interceptor.TenantInterceptor;
import ai.dify.tenant.mybatis.TenantSqlInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 租户配置
 */
@Configuration
@RequiredArgsConstructor
public class TenantConfig implements WebMvcConfigurer {

    private final TenantInterceptor tenantInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/actuator/**",
                    "/swagger-ui/**",
                    "/v3/api-docs/**",
                    "/error",
                    "/favicon.ico",
                    "/static/**",
                    "/public/**",
                    "/console/api/setup",
                    "/console/api/login",
                    "/console/api/logout"
                );
    }
}
