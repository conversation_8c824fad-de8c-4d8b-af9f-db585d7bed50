package ai.dify.tenant.context;

import ai.dify.tenant.model.Tenant;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户上下文
 */
@Slf4j
public class TenantContext {

    private static final ThreadLocal<String> TENANT_ID = new ThreadLocal<>();
    private static final ThreadLocal<Tenant> TENANT = new ThreadLocal<>();

    /**
     * 设置当前租户ID
     */
    public static void setTenantId(String tenantId) {
        TENANT_ID.set(tenantId);
        log.debug("设置租户上下文: tenantId={}", tenantId);
    }

    /**
     * 获取当前租户ID
     */
    public static String getTenantId() {
        return TENANT_ID.get();
    }

    /**
     * 设置当前租户
     */
    public static void setTenant(Tenant tenant) {
        TENANT.set(tenant);
        if (tenant != null) {
            setTenantId(tenant.getId());
        }
    }

    /**
     * 获取当前租户
     */
    public static Tenant getTenant() {
        return TENANT.get();
    }

    /**
     * 检查是否有租户上下文
     */
    public static boolean hasTenant() {
        return getTenantId() != null;
    }

    /**
     * 清除租户上下文
     */
    public static void clear() {
        String tenantId = getTenantId();
        TENANT_ID.remove();
        TENANT.remove();
        log.debug("清除租户上下文: tenantId={}", tenantId);
    }

    /**
     * 获取租户数据库前缀
     */
    public static String getTenantDbPrefix() {
        String tenantId = getTenantId();
        if (tenantId == null) {
            return "";
        }
        return "t_" + tenantId + "_";
    }

    /**
     * 获取租户缓存前缀
     */
    public static String getTenantCachePrefix() {
        String tenantId = getTenantId();
        if (tenantId == null) {
            return "";
        }
        return "tenant:" + tenantId + ":";
    }

    /**
     * 执行带租户上下文的操作
     */
    public static <T> T executeWithTenant(String tenantId, java.util.function.Supplier<T> operation) {
        String originalTenantId = getTenantId();
        try {
            setTenantId(tenantId);
            return operation.get();
        } finally {
            if (originalTenantId != null) {
                setTenantId(originalTenantId);
            } else {
                clear();
            }
        }
    }

    /**
     * 执行带租户上下文的操作（无返回值）
     */
    public static void executeWithTenant(String tenantId, Runnable operation) {
        String originalTenantId = getTenantId();
        try {
            setTenantId(tenantId);
            operation.run();
        } finally {
            if (originalTenantId != null) {
                setTenantId(originalTenantId);
            } else {
                clear();
            }
        }
    }
}
