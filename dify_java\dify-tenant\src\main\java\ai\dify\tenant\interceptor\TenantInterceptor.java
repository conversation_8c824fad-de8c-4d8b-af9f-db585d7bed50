package ai.dify.tenant.interceptor;

import ai.dify.tenant.context.TenantContext;
import ai.dify.tenant.model.Tenant;
import ai.dify.tenant.service.TenantService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 租户拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantInterceptor implements HandlerInterceptor {

    private final TenantService tenantService;

    private static final String TENANT_HEADER = "X-Tenant-Id";
    private static final String TENANT_CODE_HEADER = "X-Tenant-Code";
    private static final String TENANT_PARAM = "tenant";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            String tenantId = resolveTenantId(request);
            
            if (tenantId != null) {
                // 获取租户信息
                Tenant tenant = tenantService.getTenantById(tenantId);
                
                if (tenant == null) {
                    log.warn("租户不存在: tenantId={}", tenantId);
                    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    response.getWriter().write("{\"error\":\"租户不存在\"}");
                    return false;
                }
                
                if (!tenant.isActive()) {
                    log.warn("租户不活跃: tenantId={}, status={}", tenantId, tenant.getStatus());
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write("{\"error\":\"租户不活跃\"}");
                    return false;
                }
                
                // 设置租户上下文
                TenantContext.setTenant(tenant);
                
                log.debug("租户上下文已设置: tenantId={}, tenantCode={}", tenant.getId(), tenant.getCode());
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("租户拦截器处理失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"error\":\"服务器内部错误\"}");
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 清除租户上下文
        TenantContext.clear();
    }

    /**
     * 解析租户ID
     */
    private String resolveTenantId(HttpServletRequest request) {
        // 1. 从请求头获取租户ID
        String tenantId = request.getHeader(TENANT_HEADER);
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            return tenantId.trim();
        }
        
        // 2. 从请求头获取租户代码，然后查找租户ID
        String tenantCode = request.getHeader(TENANT_CODE_HEADER);
        if (tenantCode != null && !tenantCode.trim().isEmpty()) {
            Tenant tenant = tenantService.getTenantByCode(tenantCode.trim());
            if (tenant != null) {
                return tenant.getId();
            }
        }
        
        // 3. 从请求参数获取租户ID
        tenantId = request.getParameter(TENANT_PARAM);
        if (tenantId != null && !tenantId.trim().isEmpty()) {
            return tenantId.trim();
        }
        
        // 4. 从子域名解析租户代码
        String host = request.getServerName();
        if (host != null && host.contains(".")) {
            String subdomain = host.substring(0, host.indexOf("."));
            if (!subdomain.equals("www") && !subdomain.equals("api")) {
                Tenant tenant = tenantService.getTenantByCode(subdomain);
                if (tenant != null) {
                    return tenant.getId();
                }
            }
        }
        
        // 5. 从路径解析租户代码
        String path = request.getRequestURI();
        if (path.startsWith("/tenant/")) {
            String[] parts = path.split("/");
            if (parts.length > 2) {
                String tenantCode2 = parts[2];
                Tenant tenant = tenantService.getTenantByCode(tenantCode2);
                if (tenant != null) {
                    return tenant.getId();
                }
            }
        }
        
        return null;
    }
}
