package ai.dify.tenant.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 租户实体
 */
@Data
@TableName("tenants")
public class Tenant {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 租户名称
     */
    private String name;

    /**
     * 租户代码（唯一标识）
     */
    private String code;

    /**
     * 租户描述
     */
    private String description;

    /**
     * 租户状态
     */
    private TenantStatus status;

    /**
     * 租户类型
     */
    private TenantType type;

    /**
     * 租户配置
     */
    private Map<String, Object> config;

    /**
     * 资源配额
     */
    private TenantQuota quota;

    /**
     * 联系人信息
     */
    private String contactName;
    private String contactEmail;
    private String contactPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 租户状态枚举
     */
    public enum TenantStatus {
        /**
         * 活跃
         */
        ACTIVE,

        /**
         * 暂停
         */
        SUSPENDED,

        /**
         * 已过期
         */
        EXPIRED,

        /**
         * 已删除
         */
        DELETED
    }

    /**
     * 租户类型枚举
     */
    public enum TenantType {
        /**
         * 免费版
         */
        FREE,

        /**
         * 基础版
         */
        BASIC,

        /**
         * 专业版
         */
        PROFESSIONAL,

        /**
         * 企业版
         */
        ENTERPRISE,

        /**
         * 自定义版
         */
        CUSTOM
    }

    /**
     * 检查租户是否活跃
     */
    public boolean isActive() {
        return status == TenantStatus.ACTIVE && 
               (expiresAt == null || expiresAt.isAfter(LocalDateTime.now()));
    }

    /**
     * 检查租户是否过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    /**
     * 获取配置值
     */
    public Object getConfigValue(String key) {
        return config != null ? config.get(key) : null;
    }

    /**
     * 设置配置值
     */
    public void setConfigValue(String key, Object value) {
        if (config == null) {
            config = new java.util.HashMap<>();
        }
        config.put(key, value);
    }
}
