package ai.dify.tenant.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 租户配额
 */
@Data
public class TenantQuota implements Serializable {

    /**
     * 最大应用数量
     */
    private Integer maxApps = 10;

    /**
     * 最大用户数量
     */
    private Integer maxUsers = 100;

    /**
     * 最大数据集数量
     */
    private Integer maxDatasets = 50;

    /**
     * 最大文档数量
     */
    private Integer maxDocuments = 1000;

    /**
     * 最大存储空间（MB）
     */
    private Long maxStorageMb = 1024L;

    /**
     * 每月最大API调用次数
     */
    private Long maxApiCallsPerMonth = 100000L;

    /**
     * 每日最大API调用次数
     */
    private Long maxApiCallsPerDay = 10000L;

    /**
     * 每小时最大API调用次数
     */
    private Long maxApiCallsPerHour = 1000L;

    /**
     * 最大并发请求数
     */
    private Integer maxConcurrentRequests = 10;

    /**
     * 最大工作流数量
     */
    private Integer maxWorkflows = 20;

    /**
     * 最大工作流节点数
     */
    private Integer maxWorkflowNodes = 100;

    /**
     * 是否支持自定义模型
     */
    private Boolean customModelEnabled = false;

    /**
     * 是否支持插件
     */
    private Boolean pluginEnabled = false;

    /**
     * 是否支持API访问
     */
    private Boolean apiEnabled = true;

    /**
     * 是否支持SSO
     */
    private Boolean ssoEnabled = false;

    /**
     * 数据保留天数
     */
    private Integer dataRetentionDays = 30;

    /**
     * 创建默认配额
     */
    public static TenantQuota createDefault() {
        return new TenantQuota();
    }

    /**
     * 创建免费版配额
     */
    public static TenantQuota createFree() {
        TenantQuota quota = new TenantQuota();
        quota.setMaxApps(3);
        quota.setMaxUsers(5);
        quota.setMaxDatasets(5);
        quota.setMaxDocuments(100);
        quota.setMaxStorageMb(100L);
        quota.setMaxApiCallsPerMonth(10000L);
        quota.setMaxApiCallsPerDay(1000L);
        quota.setMaxApiCallsPerHour(100L);
        quota.setMaxConcurrentRequests(2);
        quota.setMaxWorkflows(5);
        quota.setMaxWorkflowNodes(20);
        quota.setCustomModelEnabled(false);
        quota.setPluginEnabled(false);
        quota.setSsoEnabled(false);
        quota.setDataRetentionDays(7);
        return quota;
    }

    /**
     * 创建基础版配额
     */
    public static TenantQuota createBasic() {
        TenantQuota quota = new TenantQuota();
        quota.setMaxApps(10);
        quota.setMaxUsers(20);
        quota.setMaxDatasets(20);
        quota.setMaxDocuments(500);
        quota.setMaxStorageMb(500L);
        quota.setMaxApiCallsPerMonth(50000L);
        quota.setMaxApiCallsPerDay(5000L);
        quota.setMaxApiCallsPerHour(500L);
        quota.setMaxConcurrentRequests(5);
        quota.setMaxWorkflows(15);
        quota.setMaxWorkflowNodes(50);
        quota.setCustomModelEnabled(false);
        quota.setPluginEnabled(true);
        quota.setSsoEnabled(false);
        quota.setDataRetentionDays(30);
        return quota;
    }

    /**
     * 创建专业版配额
     */
    public static TenantQuota createProfessional() {
        TenantQuota quota = new TenantQuota();
        quota.setMaxApps(50);
        quota.setMaxUsers(100);
        quota.setMaxDatasets(100);
        quota.setMaxDocuments(5000);
        quota.setMaxStorageMb(5120L);
        quota.setMaxApiCallsPerMonth(500000L);
        quota.setMaxApiCallsPerDay(50000L);
        quota.setMaxApiCallsPerHour(5000L);
        quota.setMaxConcurrentRequests(20);
        quota.setMaxWorkflows(100);
        quota.setMaxWorkflowNodes(200);
        quota.setCustomModelEnabled(true);
        quota.setPluginEnabled(true);
        quota.setSsoEnabled(true);
        quota.setDataRetentionDays(90);
        return quota;
    }

    /**
     * 创建企业版配额
     */
    public static TenantQuota createEnterprise() {
        TenantQuota quota = new TenantQuota();
        quota.setMaxApps(-1); // 无限制
        quota.setMaxUsers(-1);
        quota.setMaxDatasets(-1);
        quota.setMaxDocuments(-1);
        quota.setMaxStorageMb(-1L);
        quota.setMaxApiCallsPerMonth(-1L);
        quota.setMaxApiCallsPerDay(-1L);
        quota.setMaxApiCallsPerHour(-1L);
        quota.setMaxConcurrentRequests(100);
        quota.setMaxWorkflows(-1);
        quota.setMaxWorkflowNodes(-1);
        quota.setCustomModelEnabled(true);
        quota.setPluginEnabled(true);
        quota.setSsoEnabled(true);
        quota.setDataRetentionDays(365);
        return quota;
    }

    /**
     * 检查是否超出限制
     */
    public boolean isWithinLimit(String resource, long currentValue) {
        switch (resource) {
            case "apps":
                return maxApps == -1 || currentValue < maxApps;
            case "users":
                return maxUsers == -1 || currentValue < maxUsers;
            case "datasets":
                return maxDatasets == -1 || currentValue < maxDatasets;
            case "documents":
                return maxDocuments == -1 || currentValue < maxDocuments;
            case "storage":
                return maxStorageMb == -1 || currentValue < maxStorageMb;
            case "workflows":
                return maxWorkflows == -1 || currentValue < maxWorkflows;
            default:
                return true;
        }
    }
}
