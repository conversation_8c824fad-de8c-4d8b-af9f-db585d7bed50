package ai.dify.tenant.mybatis;

import ai.dify.tenant.context.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 租户SQL拦截器
 * 自动为SQL添加租户隔离条件
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class TenantSqlInterceptor implements Interceptor {

    private static final Pattern TABLE_PATTERN = Pattern.compile("(FROM|JOIN|INTO|UPDATE)\\s+([a-zA-Z_][a-zA-Z0-9_]*)", Pattern.CASE_INSENSITIVE);
    
    // 不需要租户隔离的表
    private static final String[] EXCLUDED_TABLES = {
        "tenants",
        "users", 
        "system_config",
        "migrations",
        "oauth_providers"
    };

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            String tenantId = TenantContext.getTenantId();
            
            // 如果没有租户上下文，直接执行原SQL
            if (tenantId == null) {
                return invocation.proceed();
            }
            
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            Object parameter = invocation.getArgs()[1];
            
            // 获取原始SQL
            BoundSql boundSql = mappedStatement.getBoundSql(parameter);
            String originalSql = boundSql.getSql();
            
            // 处理SQL
            String modifiedSql = processSql(originalSql, tenantId, mappedStatement.getSqlCommandType());
            
            if (!originalSql.equals(modifiedSql)) {
                log.debug("SQL已修改添加租户隔离: tenantId={}", tenantId);
                log.debug("原始SQL: {}", originalSql);
                log.debug("修改后SQL: {}", modifiedSql);
                
                // 创建新的BoundSql
                BoundSql newBoundSql = new BoundSql(
                    mappedStatement.getConfiguration(),
                    modifiedSql,
                    boundSql.getParameterMappings(),
                    parameter
                );
                
                // 复制额外参数
                for (String key : boundSql.getAdditionalParameterNames()) {
                    newBoundSql.setAdditionalParameter(key, boundSql.getAdditionalParameter(key));
                }
                
                // 创建新的MappedStatement
                MappedStatement newMappedStatement = copyMappedStatement(mappedStatement, newBoundSql);
                invocation.getArgs()[0] = newMappedStatement;
            }
            
            return invocation.proceed();
            
        } catch (Exception e) {
            log.error("租户SQL拦截器处理失败", e);
            // 发生异常时执行原SQL
            return invocation.proceed();
        }
    }

    /**
     * 处理SQL，添加租户隔离条件
     */
    private String processSql(String sql, String tenantId, SqlCommandType commandType) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }
        
        String processedSql = sql;
        
        switch (commandType) {
            case SELECT:
                processedSql = processSelectSql(sql, tenantId);
                break;
            case INSERT:
                processedSql = processInsertSql(sql, tenantId);
                break;
            case UPDATE:
                processedSql = processUpdateSql(sql, tenantId);
                break;
            case DELETE:
                processedSql = processDeleteSql(sql, tenantId);
                break;
            default:
                break;
        }
        
        return processedSql;
    }

    /**
     * 处理SELECT语句
     */
    private String processSelectSql(String sql, String tenantId) {
        // 简化实现：为主要业务表添加租户条件
        if (containsBusinessTable(sql)) {
            // 如果已经有WHERE子句，添加AND条件
            if (sql.toUpperCase().contains("WHERE")) {
                return sql + " AND tenant_id = '" + tenantId + "'";
            } else {
                // 如果没有WHERE子句，添加WHERE条件
                return sql + " WHERE tenant_id = '" + tenantId + "'";
            }
        }
        return sql;
    }

    /**
     * 处理INSERT语句
     */
    private String processInsertSql(String sql, String tenantId) {
        if (containsBusinessTable(sql)) {
            // 为INSERT语句添加tenant_id字段
            Pattern insertPattern = Pattern.compile("INSERT\\s+INTO\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
            Matcher matcher = insertPattern.matcher(sql);
            
            if (matcher.find()) {
                String tableName = matcher.group(1);
                String columns = matcher.group(2);
                
                if (!isExcludedTable(tableName) && !columns.contains("tenant_id")) {
                    String newColumns = columns + ", tenant_id";
                    String newSql = sql.replace("(" + columns + ")", "(" + newColumns + ")");
                    
                    // 添加tenant_id值
                    Pattern valuesPattern = Pattern.compile("VALUES\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
                    Matcher valuesMatcher = valuesPattern.matcher(newSql);
                    
                    if (valuesMatcher.find()) {
                        String values = valuesMatcher.group(1);
                        String newValues = values + ", '" + tenantId + "'";
                        return newSql.replace("(" + values + ")", "(" + newValues + ")");
                    }
                }
            }
        }
        return sql;
    }

    /**
     * 处理UPDATE语句
     */
    private String processUpdateSql(String sql, String tenantId) {
        if (containsBusinessTable(sql)) {
            if (sql.toUpperCase().contains("WHERE")) {
                return sql + " AND tenant_id = '" + tenantId + "'";
            } else {
                return sql + " WHERE tenant_id = '" + tenantId + "'";
            }
        }
        return sql;
    }

    /**
     * 处理DELETE语句
     */
    private String processDeleteSql(String sql, String tenantId) {
        if (containsBusinessTable(sql)) {
            if (sql.toUpperCase().contains("WHERE")) {
                return sql + " AND tenant_id = '" + tenantId + "'";
            } else {
                return sql + " WHERE tenant_id = '" + tenantId + "'";
            }
        }
        return sql;
    }

    /**
     * 检查SQL是否包含业务表
     */
    private boolean containsBusinessTable(String sql) {
        Matcher matcher = TABLE_PATTERN.matcher(sql);
        while (matcher.find()) {
            String tableName = matcher.group(2);
            if (!isExcludedTable(tableName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否是排除的表
     */
    private boolean isExcludedTable(String tableName) {
        for (String excludedTable : EXCLUDED_TABLES) {
            if (excludedTable.equalsIgnoreCase(tableName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 复制MappedStatement
     */
    private MappedStatement copyMappedStatement(MappedStatement ms, BoundSql newBoundSql) {
        MappedStatement.Builder builder = new MappedStatement.Builder(
            ms.getConfiguration(),
            ms.getId(),
            parameterObject -> newBoundSql,
            ms.getSqlCommandType()
        );
        
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());
        
        if (ms.getKeyProperties() != null && ms.getKeyProperties().length > 0) {
            builder.keyProperty(String.join(",", ms.getKeyProperties()));
        }
        
        return builder.build();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件读取排除的表名等配置
    }
}
