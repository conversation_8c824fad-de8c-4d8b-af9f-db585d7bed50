package ai.dify.tools.builtin;

import ai.dify.agent.tool.Tool;
import ai.dify.agent.tool.ToolCall;
import ai.dify.agent.tool.ToolResult;
import ai.dify.agent.tool.annotation.ToolDefinition;
import ai.dify.agent.tool.annotation.ToolParameter;
import lombok.extern.slf4j.Slf4j;
import net.objecthunter.exp4j.Expression;
import net.objecthunter.exp4j.ExpressionBuilder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 计算器工具
 */
@Slf4j
@Component
@ToolDefinition(
    name = "calculator",
    description = "执行数学计算，支持基本运算、函数和常量",
    category = "math"
)
public class CalculatorTool implements Tool {

    private static final Pattern SAFE_EXPRESSION_PATTERN = 
        Pattern.compile("^[0-9+\\-*/().\\s\\w]+$");

    @Override
    public String getName() {
        return "calculator";
    }

    @Override
    public String getDescription() {
        return "执行数学计算，支持基本运算（+、-、*、/）、幂运算（^）、括号、数学函数（sin、cos、tan、log、sqrt等）和常量（pi、e）";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public ToolResult execute(ToolCall toolCall) {
        try {
            String expression = getStringParameter(toolCall, "expression");
            if (expression == null || expression.trim().isEmpty()) {
                return ToolResult.failure("表达式不能为空");
            }

            // 安全检查
            if (!isSafeExpression(expression)) {
                return ToolResult.failure("表达式包含不安全的字符");
            }

            // 计算结果
            double result = calculateExpression(expression);
            
            // 格式化结果
            String formattedResult = formatResult(result);
            
            Map<String, Object> output = new HashMap<>();
            output.put("expression", expression);
            output.put("result", result);
            output.put("formatted_result", formattedResult);
            
            return ToolResult.success(output);
            
        } catch (Exception e) {
            log.error("计算失败", e);
            return ToolResult.failure("计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算表达式
     */
    private double calculateExpression(String expression) throws Exception {
        try {
            // 预处理表达式
            String processedExpression = preprocessExpression(expression);
            
            // 使用 exp4j 计算
            Expression exp = new ExpressionBuilder(processedExpression)
                    .build();
            
            double result = exp.evaluate();
            
            // 检查结果是否有效
            if (Double.isNaN(result)) {
                throw new ArithmeticException("计算结果为 NaN");
            }
            if (Double.isInfinite(result)) {
                throw new ArithmeticException("计算结果为无穷大");
            }
            
            return result;
            
        } catch (Exception e) {
            throw new Exception("表达式计算错误: " + e.getMessage());
        }
    }

    /**
     * 预处理表达式
     */
    private String preprocessExpression(String expression) {
        // 移除空格
        expression = expression.replaceAll("\\s+", "");
        
        // 替换常见的数学符号
        expression = expression.replace("×", "*");
        expression = expression.replace("÷", "/");
        expression = expression.replace("π", "pi");
        
        // 处理隐式乘法，如 2(3+4) -> 2*(3+4)
        expression = expression.replaceAll("(\\d)\\(", "$1*(");
        expression = expression.replaceAll("\\)(\\d)", ")*$1");
        expression = expression.replaceAll("\\)\\(", ")*(");
        
        return expression;
    }

    /**
     * 检查表达式是否安全
     */
    private boolean isSafeExpression(String expression) {
        // 基本字符检查
        if (!SAFE_EXPRESSION_PATTERN.matcher(expression).matches()) {
            return false;
        }
        
        // 长度检查
        if (expression.length() > 1000) {
            return false;
        }
        
        // 检查是否包含危险函数或关键字
        String lowerExpression = expression.toLowerCase();
        String[] dangerousKeywords = {
            "system", "exec", "eval", "import", "class", "while", "for"
        };
        
        for (String keyword : dangerousKeywords) {
            if (lowerExpression.contains(keyword)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 格式化结果
     */
    private String formatResult(double result) {
        // 如果是整数，显示为整数
        if (result == Math.floor(result) && !Double.isInfinite(result)) {
            return String.valueOf((long) result);
        }
        
        // 使用 BigDecimal 进行精确格式化
        BigDecimal bd = BigDecimal.valueOf(result);
        bd = bd.setScale(10, RoundingMode.HALF_UP);
        
        // 移除尾随零
        bd = bd.stripTrailingZeros();
        
        return bd.toPlainString();
    }

    @Override
    public boolean validateCall(ToolCall toolCall) {
        String expression = getStringParameter(toolCall, "expression");
        return expression != null && !expression.trim().isEmpty() && 
               isSafeExpression(expression);
    }

    @Override
    public List<ToolParameter> getParameters() {
        return List.of(
            ToolParameter.builder()
                .name("expression")
                .type("string")
                .description("要计算的数学表达式，支持基本运算、函数和常量。例如：'2+3*4', 'sin(pi/2)', 'sqrt(16)', 'log(10)'")
                .required(true)
                .build()
        );
    }
}
