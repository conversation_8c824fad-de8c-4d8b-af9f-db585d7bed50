package ai.dify.tools.builtin;

import ai.dify.agent.tool.Tool;
import ai.dify.agent.tool.ToolCall;
import ai.dify.agent.tool.ToolResult;
import ai.dify.agent.tool.annotation.ToolDefinition;
import ai.dify.agent.tool.annotation.ToolParameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 邮件发送工具
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ToolDefinition(
    name = "email",
    description = "发送邮件",
    category = "communication"
)
public class EmailTool implements Tool {

    private final JavaMailSender mailSender;

    @Value("${spring.mail.username:}")
    private String fromEmail;

    @Value("${dify.tools.email.enabled:false}")
    private boolean emailEnabled;

    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$");

    @Override
    public String getName() {
        return "email";
    }

    @Override
    public String getDescription() {
        return "发送邮件到指定的邮箱地址";
    }

    @Override
    public boolean isAvailable() {
        return emailEnabled && mailSender != null && 
               fromEmail != null && !fromEmail.trim().isEmpty();
    }

    @Override
    public ToolResult execute(ToolCall toolCall) {
        try {
            String to = getStringParameter(toolCall, "to");
            String subject = getStringParameter(toolCall, "subject");
            String content = getStringParameter(toolCall, "content");
            String cc = getStringParameter(toolCall, "cc");
            String bcc = getStringParameter(toolCall, "bcc");
            Boolean isHtml = getBooleanParameter(toolCall, "is_html", false);

            // 验证参数
            if (to == null || to.trim().isEmpty()) {
                return ToolResult.failure("收件人邮箱不能为空");
            }
            
            if (!isValidEmail(to)) {
                return ToolResult.failure("收件人邮箱格式无效");
            }
            
            if (subject == null || subject.trim().isEmpty()) {
                return ToolResult.failure("邮件主题不能为空");
            }
            
            if (content == null || content.trim().isEmpty()) {
                return ToolResult.failure("邮件内容不能为空");
            }

            // 发送邮件
            sendEmail(to, subject, content, cc, bcc, isHtml);
            
            Map<String, Object> result = new HashMap<>();
            result.put("to", to);
            result.put("subject", subject);
            result.put("status", "sent");
            result.put("sent_at", java.time.LocalDateTime.now());
            
            return ToolResult.success(result);
            
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return ToolResult.failure("发送邮件失败: " + e.getMessage());
        }
    }

    /**
     * 发送邮件
     */
    private void sendEmail(String to, String subject, String content, 
                          String cc, String bcc, boolean isHtml) throws Exception {
        if (isHtml) {
            // 发送HTML邮件
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            
            if (cc != null && !cc.trim().isEmpty() && isValidEmail(cc)) {
                helper.setCc(cc);
            }
            
            if (bcc != null && !bcc.trim().isEmpty() && isValidEmail(bcc)) {
                helper.setBcc(bcc);
            }
            
            mailSender.send(message);
            
        } else {
            // 发送纯文本邮件
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            
            if (cc != null && !cc.trim().isEmpty() && isValidEmail(cc)) {
                message.setCc(cc);
            }
            
            if (bcc != null && !bcc.trim().isEmpty() && isValidEmail(bcc)) {
                message.setBcc(bcc);
            }
            
            mailSender.send(message);
        }
        
        log.info("邮件发送成功: to={}, subject={}", to, subject);
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    @Override
    public boolean validateCall(ToolCall toolCall) {
        String to = getStringParameter(toolCall, "to");
        String subject = getStringParameter(toolCall, "subject");
        String content = getStringParameter(toolCall, "content");
        
        return to != null && !to.trim().isEmpty() && isValidEmail(to) &&
               subject != null && !subject.trim().isEmpty() &&
               content != null && !content.trim().isEmpty();
    }

    @Override
    public List<ToolParameter> getParameters() {
        return List.of(
            ToolParameter.builder()
                .name("to")
                .type("string")
                .description("收件人邮箱地址")
                .required(true)
                .build(),
            ToolParameter.builder()
                .name("subject")
                .type("string")
                .description("邮件主题")
                .required(true)
                .build(),
            ToolParameter.builder()
                .name("content")
                .type("string")
                .description("邮件内容")
                .required(true)
                .build(),
            ToolParameter.builder()
                .name("cc")
                .type("string")
                .description("抄送邮箱地址")
                .required(false)
                .build(),
            ToolParameter.builder()
                .name("bcc")
                .type("string")
                .description("密送邮箱地址")
                .required(false)
                .build(),
            ToolParameter.builder()
                .name("is_html")
                .type("boolean")
                .description("是否为HTML格式邮件")
                .required(false)
                .defaultValue(false)
                .build()
        );
    }
}
