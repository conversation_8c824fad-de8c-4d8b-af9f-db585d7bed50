package ai.dify.tools.builtin;

import ai.dify.agent.tool.Tool;
import ai.dify.agent.tool.ToolCall;
import ai.dify.agent.tool.ToolResult;
import ai.dify.agent.tool.annotation.ToolDefinition;
import ai.dify.agent.tool.annotation.ToolParameter;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Google 搜索工具
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ToolDefinition(
    name = "google_search",
    description = "使用Google搜索引擎搜索信息",
    category = "search"
)
public class GoogleSearchTool implements Tool {

    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    @Value("${dify.tools.google.api-key:}")
    private String googleApiKey;

    @Value("${dify.tools.google.search-engine-id:}")
    private String searchEngineId;

    @Value("${dify.tools.google.max-results:10}")
    private int maxResults;

    @Override
    public String getName() {
        return "google_search";
    }

    @Override
    public String getDescription() {
        return "使用Google搜索引擎搜索信息，返回相关的搜索结果";
    }

    @Override
    public boolean isAvailable() {
        return googleApiKey != null && !googleApiKey.trim().isEmpty() &&
               searchEngineId != null && !searchEngineId.trim().isEmpty();
    }

    @Override
    public ToolResult execute(ToolCall toolCall) {
        try {
            // 获取搜索参数
            String query = getStringParameter(toolCall, "query");
            Integer numResults = getIntParameter(toolCall, "num_results", maxResults);
            String language = getStringParameter(toolCall, "language", "zh-CN");
            
            if (query == null || query.trim().isEmpty()) {
                return ToolResult.failure("搜索查询不能为空");
            }

            // 执行搜索
            List<SearchResult> results = performSearch(query, numResults, language);
            
            // 构建结果
            Map<String, Object> output = new HashMap<>();
            output.put("query", query);
            output.put("results", results);
            output.put("total_results", results.size());
            
            return ToolResult.success(output);
            
        } catch (Exception e) {
            log.error("Google搜索失败", e);
            return ToolResult.failure("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 执行搜索
     */
    private List<SearchResult> performSearch(String query, int numResults, String language) throws Exception {
        String url = String.format(
            "https://www.googleapis.com/customsearch/v1?key=%s&cx=%s&q=%s&num=%d&lr=lang_%s",
            googleApiKey, searchEngineId, 
            java.net.URLEncoder.encode(query, "UTF-8"),
            Math.min(numResults, 10), // Google API 最多返回10个结果
            language.split("-")[0] // 只取语言代码部分
        );

        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new RuntimeException("搜索请求失败: " + response.code());
            }

            String responseBody = response.body().string();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            List<SearchResult> results = new ArrayList<>();
            JsonNode items = jsonNode.get("items");
            
            if (items != null && items.isArray()) {
                for (JsonNode item : items) {
                    SearchResult result = new SearchResult();
                    result.setTitle(item.get("title").asText());
                    result.setLink(item.get("link").asText());
                    result.setSnippet(item.get("snippet").asText());
                    
                    // 获取显示链接
                    JsonNode displayLink = item.get("displayLink");
                    if (displayLink != null) {
                        result.setDisplayLink(displayLink.asText());
                    }
                    
                    results.add(result);
                }
            }
            
            return results;
        }
    }

    @Override
    public boolean validateCall(ToolCall toolCall) {
        String query = getStringParameter(toolCall, "query");
        return query != null && !query.trim().isEmpty();
    }

    @Override
    public List<ToolParameter> getParameters() {
        return List.of(
            ToolParameter.builder()
                .name("query")
                .type("string")
                .description("搜索查询关键词")
                .required(true)
                .build(),
            ToolParameter.builder()
                .name("num_results")
                .type("integer")
                .description("返回结果数量，最大10个")
                .required(false)
                .defaultValue(maxResults)
                .build(),
            ToolParameter.builder()
                .name("language")
                .type("string")
                .description("搜索语言，如zh-CN、en-US等")
                .required(false)
                .defaultValue("zh-CN")
                .build()
        );
    }

    /**
     * 搜索结果
     */
    public static class SearchResult {
        private String title;
        private String link;
        private String snippet;
        private String displayLink;

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getLink() { return link; }
        public void setLink(String link) { this.link = link; }
        
        public String getSnippet() { return snippet; }
        public void setSnippet(String snippet) { this.snippet = snippet; }
        
        public String getDisplayLink() { return displayLink; }
        public void setDisplayLink(String displayLink) { this.displayLink = displayLink; }
    }
}
