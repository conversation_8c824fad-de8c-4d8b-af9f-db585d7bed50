package ai.dify.tools.builtin;

import ai.dify.agent.tool.Tool;
import ai.dify.agent.tool.ToolCall;
import ai.dify.agent.tool.ToolResult;
import ai.dify.agent.tool.annotation.ToolDefinition;
import ai.dify.agent.tool.annotation.ToolParameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 网页抓取工具
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ToolDefinition(
    name = "web_scraping",
    description = "抓取网页内容并提取文本、链接等信息",
    category = "web"
)
public class WebScrapingTool implements Tool {

    private final OkHttpClient httpClient;

    @Override
    public String getName() {
        return "web_scraping";
    }

    @Override
    public String getDescription() {
        return "抓取指定网页的内容，提取文本、标题、链接等信息";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public ToolResult execute(ToolCall toolCall) {
        try {
            String url = getStringParameter(toolCall, "url");
            String selector = getStringParameter(toolCall, "selector");
            Boolean extractLinks = getBooleanParameter(toolCall, "extract_links", false);
            Boolean extractImages = getBooleanParameter(toolCall, "extract_images", false);
            Integer maxLength = getIntParameter(toolCall, "max_length", 10000);

            if (url == null || url.trim().isEmpty()) {
                return ToolResult.failure("URL不能为空");
            }

            // 验证URL格式
            if (!isValidUrl(url)) {
                return ToolResult.failure("无效的URL格式");
            }

            // 抓取网页内容
            String html = fetchWebPage(url);
            
            // 解析HTML
            Document doc = Jsoup.parse(html);
            
            // 提取内容
            Map<String, Object> result = extractContent(doc, selector, extractLinks, extractImages, maxLength);
            result.put("url", url);
            
            return ToolResult.success(result);
            
        } catch (Exception e) {
            log.error("网页抓取失败", e);
            return ToolResult.failure("网页抓取失败: " + e.getMessage());
        }
    }

    /**
     * 抓取网页内容
     */
    private String fetchWebPage(String url) throws Exception {
        Request request = new Request.Builder()
                .url(url)
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new RuntimeException("HTTP请求失败: " + response.code());
            }

            return response.body().string();
        }
    }

    /**
     * 提取内容
     */
    private Map<String, Object> extractContent(Document doc, String selector, 
                                             boolean extractLinks, boolean extractImages, int maxLength) {
        Map<String, Object> result = new HashMap<>();
        
        // 提取标题
        String title = doc.title();
        result.put("title", title);
        
        // 提取主要文本内容
        String text;
        if (selector != null && !selector.trim().isEmpty()) {
            Elements elements = doc.select(selector);
            text = elements.text();
        } else {
            // 移除脚本和样式标签
            doc.select("script, style").remove();
            text = doc.body().text();
        }
        
        // 限制文本长度
        if (text.length() > maxLength) {
            text = text.substring(0, maxLength) + "...";
        }
        result.put("text", text);
        result.put("text_length", text.length());
        
        // 提取元数据
        Map<String, String> metadata = new HashMap<>();
        Elements metaTags = doc.select("meta");
        for (Element meta : metaTags) {
            String name = meta.attr("name");
            String property = meta.attr("property");
            String content = meta.attr("content");
            
            if (!name.isEmpty() && !content.isEmpty()) {
                metadata.put(name, content);
            } else if (!property.isEmpty() && !content.isEmpty()) {
                metadata.put(property, content);
            }
        }
        result.put("metadata", metadata);
        
        // 提取链接
        if (extractLinks) {
            List<Map<String, String>> links = new ArrayList<>();
            Elements linkElements = doc.select("a[href]");
            
            for (Element link : linkElements) {
                String href = link.attr("abs:href");
                String linkText = link.text();
                
                if (!href.isEmpty()) {
                    Map<String, String> linkInfo = new HashMap<>();
                    linkInfo.put("url", href);
                    linkInfo.put("text", linkText);
                    links.add(linkInfo);
                }
            }
            result.put("links", links);
        }
        
        // 提取图片
        if (extractImages) {
            List<Map<String, String>> images = new ArrayList<>();
            Elements imgElements = doc.select("img[src]");
            
            for (Element img : imgElements) {
                String src = img.attr("abs:src");
                String alt = img.attr("alt");
                
                if (!src.isEmpty()) {
                    Map<String, String> imgInfo = new HashMap<>();
                    imgInfo.put("src", src);
                    imgInfo.put("alt", alt);
                    images.add(imgInfo);
                }
            }
            result.put("images", images);
        }
        
        return result;
    }

    /**
     * 验证URL格式
     */
    private boolean isValidUrl(String url) {
        try {
            new URL(url);
            return url.startsWith("http://") || url.startsWith("https://");
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean validateCall(ToolCall toolCall) {
        String url = getStringParameter(toolCall, "url");
        return url != null && !url.trim().isEmpty() && isValidUrl(url);
    }

    @Override
    public List<ToolParameter> getParameters() {
        return List.of(
            ToolParameter.builder()
                .name("url")
                .type("string")
                .description("要抓取的网页URL")
                .required(true)
                .build(),
            ToolParameter.builder()
                .name("selector")
                .type("string")
                .description("CSS选择器，用于提取特定元素的内容")
                .required(false)
                .build(),
            ToolParameter.builder()
                .name("extract_links")
                .type("boolean")
                .description("是否提取页面中的链接")
                .required(false)
                .defaultValue(false)
                .build(),
            ToolParameter.builder()
                .name("extract_images")
                .type("boolean")
                .description("是否提取页面中的图片")
                .required(false)
                .defaultValue(false)
                .build(),
            ToolParameter.builder()
                .name("max_length")
                .type("integer")
                .description("提取文本的最大长度")
                .required(false)
                .defaultValue(10000)
                .build()
        );
    }
}
