package ai.dify.tools.manager;

import ai.dify.agent.tool.Tool;
import ai.dify.agent.tool.ToolCall;
import ai.dify.agent.tool.ToolResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 工具管理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ToolManager {

    private final List<Tool> tools;
    private final Map<String, Tool> toolMap = new ConcurrentHashMap<>();
    private final Map<String, List<Tool>> categoryMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 注册所有工具
        for (Tool tool : tools) {
            registerTool(tool);
        }
        
        log.info("工具管理器初始化完成，共注册 {} 个工具", toolMap.size());
    }

    /**
     * 注册工具
     */
    public void registerTool(Tool tool) {
        String name = tool.getName();
        toolMap.put(name, tool);
        
        // 按类别分组
        String category = getToolCategory(tool);
        categoryMap.computeIfAbsent(category, k -> new ArrayList<>()).add(tool);
        
        log.debug("注册工具: name={}, category={}, available={}", 
                 name, category, tool.isAvailable());
    }

    /**
     * 获取工具
     */
    public Tool getTool(String name) {
        return toolMap.get(name);
    }

    /**
     * 获取所有工具
     */
    public List<Tool> getAllTools() {
        return new ArrayList<>(toolMap.values());
    }

    /**
     * 获取可用工具
     */
    public List<Tool> getAvailableTools() {
        return toolMap.values().stream()
                .filter(Tool::isAvailable)
                .collect(Collectors.toList());
    }

    /**
     * 按类别获取工具
     */
    public List<Tool> getToolsByCategory(String category) {
        return categoryMap.getOrDefault(category, Collections.emptyList());
    }

    /**
     * 获取所有类别
     */
    public Set<String> getCategories() {
        return new HashSet<>(categoryMap.keySet());
    }

    /**
     * 执行工具
     */
    public ToolResult executeTool(String toolName, ToolCall toolCall) {
        try {
            Tool tool = getTool(toolName);
            if (tool == null) {
                return ToolResult.failure("工具不存在: " + toolName);
            }

            if (!tool.isAvailable()) {
                return ToolResult.failure("工具不可用: " + toolName);
            }

            if (!tool.validateCall(toolCall)) {
                return ToolResult.failure("工具调用参数验证失败: " + toolName);
            }

            log.debug("执行工具: name={}, parameters={}", toolName, toolCall.getParameters());
            
            ToolResult result = tool.execute(toolCall);
            
            log.debug("工具执行完成: name={}, success={}", toolName, result.isSuccess());
            return result;
            
        } catch (Exception e) {
            log.error("工具执行异常: name={}", toolName, e);
            return ToolResult.failure("工具执行异常: " + e.getMessage());
        }
    }

    /**
     * 搜索工具
     */
    public List<Tool> searchTools(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAvailableTools();
        }
        
        String lowerKeyword = keyword.toLowerCase();
        
        return toolMap.values().stream()
                .filter(tool -> tool.isAvailable())
                .filter(tool -> 
                    tool.getName().toLowerCase().contains(lowerKeyword) ||
                    tool.getDescription().toLowerCase().contains(lowerKeyword) ||
                    getToolCategory(tool).toLowerCase().contains(lowerKeyword)
                )
                .collect(Collectors.toList());
    }

    /**
     * 获取工具信息
     */
    public ToolInfo getToolInfo(String name) {
        Tool tool = getTool(name);
        if (tool == null) {
            return null;
        }
        
        ToolInfo info = new ToolInfo();
        info.setName(tool.getName());
        info.setDescription(tool.getDescription());
        info.setCategory(getToolCategory(tool));
        info.setAvailable(tool.isAvailable());
        info.setParameters(tool.getParameters());
        
        return info;
    }

    /**
     * 获取所有工具信息
     */
    public List<ToolInfo> getAllToolInfos() {
        return toolMap.values().stream()
                .map(tool -> getToolInfo(tool.getName()))
                .collect(Collectors.toList());
    }

    /**
     * 获取工具统计信息
     */
    public ToolStatistics getStatistics() {
        ToolStatistics stats = new ToolStatistics();
        stats.setTotalTools(toolMap.size());
        stats.setAvailableTools((int) toolMap.values().stream().filter(Tool::isAvailable).count());
        stats.setCategories(categoryMap.size());
        
        Map<String, Integer> categoryStats = new HashMap<>();
        for (Map.Entry<String, List<Tool>> entry : categoryMap.entrySet()) {
            categoryStats.put(entry.getKey(), entry.getValue().size());
        }
        stats.setCategoryStats(categoryStats);
        
        return stats;
    }

    /**
     * 获取工具类别
     */
    private String getToolCategory(Tool tool) {
        // 尝试从注解获取类别
        Class<?> toolClass = tool.getClass();
        if (toolClass.isAnnotationPresent(ai.dify.agent.tool.annotation.ToolDefinition.class)) {
            ai.dify.agent.tool.annotation.ToolDefinition annotation = 
                toolClass.getAnnotation(ai.dify.agent.tool.annotation.ToolDefinition.class);
            String category = annotation.category();
            if (category != null && !category.trim().isEmpty()) {
                return category;
            }
        }
        
        // 默认类别
        return "general";
    }

    /**
     * 工具信息
     */
    public static class ToolInfo {
        private String name;
        private String description;
        private String category;
        private boolean available;
        private List<ai.dify.agent.tool.ToolParameter> parameters;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        
        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }
        
        public List<ai.dify.agent.tool.ToolParameter> getParameters() { return parameters; }
        public void setParameters(List<ai.dify.agent.tool.ToolParameter> parameters) { this.parameters = parameters; }
    }

    /**
     * 工具统计信息
     */
    public static class ToolStatistics {
        private int totalTools;
        private int availableTools;
        private int categories;
        private Map<String, Integer> categoryStats;

        // Getters and Setters
        public int getTotalTools() { return totalTools; }
        public void setTotalTools(int totalTools) { this.totalTools = totalTools; }
        
        public int getAvailableTools() { return availableTools; }
        public void setAvailableTools(int availableTools) { this.availableTools = availableTools; }
        
        public int getCategories() { return categories; }
        public void setCategories(int categories) { this.categories = categories; }
        
        public Map<String, Integer> getCategoryStats() { return categoryStats; }
        public void setCategoryStats(Map<String, Integer> categoryStats) { this.categoryStats = categoryStats; }
    }
}
