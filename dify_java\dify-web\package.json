{"name": "dify-web", "version": "1.0.0", "description": "Dify Java Web Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "14.0.4", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.3.3", "@types/node": "20.10.5", "@types/react": "18.2.45", "@types/react-dom": "18.2.18", "tailwindcss": "3.3.6", "autoprefixer": "10.4.16", "postcss": "8.4.32", "@headlessui/react": "1.7.17", "@heroicons/react": "2.0.18", "clsx": "2.0.0", "framer-motion": "10.16.16", "react-hook-form": "7.48.2", "@hookform/resolvers": "3.3.2", "zod": "3.22.4", "axios": "1.6.2", "swr": "2.2.4", "react-query": "3.39.3", "zustand": "4.4.7", "react-hot-toast": "2.4.1", "react-markdown": "9.0.1", "react-syntax-highlighter": "15.5.0", "@types/react-syntax-highlighter": "15.5.11", "react-flow-renderer": "10.3.17", "dagre": "0.8.5", "@types/dagre": "0.7.52", "monaco-editor": "0.45.0", "@monaco-editor/react": "4.6.0", "react-dropzone": "14.2.3", "react-virtualized": "9.22.5", "@types/react-virtualized": "9.21.29", "date-fns": "2.30.0", "lodash": "4.17.21", "@types/lodash": "4.14.202", "uuid": "9.0.1", "@types/uuid": "9.0.7"}, "devDependencies": {"eslint": "8.56.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "6.15.0", "@typescript-eslint/parser": "6.15.0", "prettier": "3.1.1", "prettier-plugin-tailwindcss": "0.5.9", "jest": "29.7.0", "@testing-library/react": "14.1.2", "@testing-library/jest-dom": "6.1.6", "jest-environment-jsdom": "29.7.0", "@storybook/react": "7.6.6", "@storybook/addon-essentials": "7.6.6", "@storybook/addon-interactions": "7.6.6", "@storybook/addon-links": "7.6.6", "@storybook/blocks": "7.6.6", "@storybook/nextjs": "7.6.6", "@storybook/testing-library": "0.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}