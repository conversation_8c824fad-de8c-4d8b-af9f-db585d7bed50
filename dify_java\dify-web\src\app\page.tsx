'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  ChartBarIcon,
  CpuChipIcon,
  DocumentTextIcon,
  UserGroupIcon,
  BoltIcon,
  ShieldCheckIcon,
  CloudIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { useAuth } from '@/hooks/useAuth'

const features = [
  {
    name: 'AI 工作流',
    description: '可视化设计和执行复杂的AI工作流，支持多种节点类型和条件分支',
    icon: BoltIcon,
    color: 'text-blue-600',
  },
  {
    name: 'RAG 系统',
    description: '检索增强生成，支持多种文档格式和智能分段',
    icon: DocumentTextIcon,
    color: 'text-green-600',
  },
  {
    name: 'Agent 助手',
    description: 'ReAct策略的智能代理，支持工具调用和多轮对话',
    icon: CpuChipIcon,
    color: 'text-purple-600',
  },
  {
    name: '多模态支持',
    description: '语音转文本、文本转语音、图像理解等多模态能力',
    icon: CloudIcon,
    color: 'text-orange-600',
  },
  {
    name: '企业级安全',
    description: 'JWT认证、API密钥、权限控制等完整的安全体系',
    icon: ShieldCheckIcon,
    color: 'text-red-600',
  },
  {
    name: '监控观测',
    description: '完整的监控、日志和链路追踪，实时掌握系统状态',
    icon: ChartBarIcon,
    color: 'text-indigo-600',
  },
  {
    name: '插件系统',
    description: '热插拔插件架构，支持功能的灵活扩展',
    icon: CodeBracketIcon,
    color: 'text-yellow-600',
  },
  {
    name: '多租户',
    description: '企业级多租户支持，数据隔离和资源管理',
    icon: UserGroupIcon,
    color: 'text-pink-600',
  },
]

const stats = [
  { name: '功能模块', value: '18+' },
  { name: 'API 端点', value: '50+' },
  { name: '工作流节点', value: '10+' },
  { name: '内置工具', value: '4+' },
]

export default function HomePage() {
  const router = useRouter()
  const { user, isLoading } = useAuth()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  const handleGetStarted = () => {
    if (user) {
      router.push('/dashboard')
    } else {
      router.push('/login')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="relative bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">
                  Dify <span className="text-blue-600">Java</span>
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {user ? (
                <Button onClick={() => router.push('/dashboard')}>
                  控制台
                </Button>
              ) : (
                <>
                  <Link href="/login">
                    <Button variant="ghost">登录</Button>
                  </Link>
                  <Link href="/register">
                    <Button>注册</Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 sm:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl sm:text-6xl font-bold text-gray-900 mb-6">
              企业级 AI 应用
              <span className="text-blue-600">开发平台</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              基于 Java 构建的现代化 AI 应用开发平台，提供工作流引擎、RAG 系统、Agent 助手等完整功能，
              助力企业快速构建和部署 AI 应用。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" onClick={handleGetStarted}>
                开始使用
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="https://github.com/your-org/dify-java">
                  查看源码
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600">{stat.name}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              强大的功能特性
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              提供完整的 AI 应用开发工具链，从模型调用到工作流编排，从知识库管理到智能代理
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="p-6 h-full hover:shadow-lg transition-shadow">
                  <div className={`w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center mb-4`}>
                    <feature.icon className={`w-6 h-6 ${feature.color}`} />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {feature.name}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {feature.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              准备开始构建您的 AI 应用了吗？
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              立即体验 Dify Java 平台，快速构建和部署您的 AI 应用
            </p>
            <Button
              size="lg"
              variant="secondary"
              onClick={handleGetStarted}
            >
              立即开始
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">
              Dify <span className="text-blue-400">Java</span>
            </h3>
            <p className="text-gray-400 mb-4">
              企业级 AI 应用开发平台
            </p>
            <p className="text-gray-500 text-sm">
              © 2024 Dify Java. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
