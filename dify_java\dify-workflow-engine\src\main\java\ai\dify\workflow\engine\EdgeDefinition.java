package ai.dify.workflow.engine;

import lombok.Data;

/**
 * 边定义（节点连接）
 */
@Data
public class EdgeDefinition {

    /**
     * 边ID
     */
    private String id;

    /**
     * 源节点ID
     */
    private String sourceNodeId;

    /**
     * 目标节点ID
     */
    private String targetNodeId;

    /**
     * 源节点输出端口
     */
    private String sourceHandle;

    /**
     * 目标节点输入端口
     */
    private String targetHandle;

    /**
     * 边的条件表达式
     */
    private String condition;

    /**
     * 边的标签
     */
    private String label;

    /**
     * 边的类型
     */
    private String type;

    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
