package ai.dify.workflow.engine;

import lombok.Data;

import java.util.Map;

/**
 * 节点定义
 */
@Data
public class NodeDefinition {

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 节点配置
     */
    private Map<String, Object> config;

    /**
     * 输入变量映射
     */
    private Map<String, String> inputMappings;

    /**
     * 输出变量映射
     */
    private Map<String, String> outputMappings;

    /**
     * 节点位置信息（用于UI显示）
     */
    private Position position;

    /**
     * 节点大小信息（用于UI显示）
     */
    private Size size;

    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 位置信息
     */
    @Data
    public static class Position {
        private Double x;
        private Double y;
    }

    /**
     * 大小信息
     */
    @Data
    public static class Size {
        private Double width;
        private Double height;
    }
}
