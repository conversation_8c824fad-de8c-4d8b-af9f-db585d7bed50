package ai.dify.workflow.engine;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 工作流定义
 */
@Data
public class WorkflowDefinition {

    /**
     * 工作流ID
     */
    private String id;

    /**
     * 工作流名称
     */
    private String name;

    /**
     * 工作流版本
     */
    private String version;

    /**
     * 工作流描述
     */
    private String description;

    /**
     * 节点定义
     */
    private Map<String, NodeDefinition> nodes;

    /**
     * 边定义（连接关系）
     */
    private Map<String, List<EdgeDefinition>> edges;

    /**
     * 环境变量
     */
    private Map<String, Object> environmentVariables;

    /**
     * 对话变量
     */
    private Map<String, Object> conversationVariables;

    /**
     * 特性配置
     */
    private Map<String, Object> features;
}
