package ai.dify.workflow.engine;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.workflow.engine.node.Node;
import ai.dify.workflow.engine.node.NodeFactory;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工作流执行引擎
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkflowEngine {

    private final NodeFactory nodeFactory;
    private final Map<String, WorkflowExecution> runningExecutions = new ConcurrentHashMap<>();

    /**
     * 执行工作流
     */
    public CompletableFuture<WorkflowResult> executeWorkflow(WorkflowDefinition definition, 
                                                           Map<String, Object> inputs) {
        String executionId = UUID.randomUUID().toString();
        log.info("开始执行工作流: executionId={}, workflowId={}", executionId, definition.getId());

        return CompletableFuture.supplyAsync(() -> {
            try {
                WorkflowExecution execution = new WorkflowExecution(executionId, definition);
                runningExecutions.put(executionId, execution);

                // 初始化变量池
                VariablePool variablePool = new VariablePool();
                variablePool.setInputs(inputs);

                // 执行工作流
                WorkflowResult result = executeGraph(execution, variablePool);
                
                log.info("工作流执行完成: executionId={}, status={}", executionId, result.getStatus());
                return result;
                
            } catch (Exception e) {
                log.error("工作流执行失败: executionId={}", executionId, e);
                return WorkflowResult.error(executionId, e.getMessage());
            } finally {
                runningExecutions.remove(executionId);
            }
        });
    }

    /**
     * 停止工作流执行
     */
    public void stopWorkflow(String executionId) {
        log.info("停止工作流执行: executionId={}", executionId);
        
        WorkflowExecution execution = runningExecutions.get(executionId);
        if (execution != null) {
            execution.stop();
        }
    }

    /**
     * 获取工作流执行状态
     */
    public WorkflowExecutionStatus getExecutionStatus(String executionId) {
        WorkflowExecution execution = runningExecutions.get(executionId);
        if (execution == null) {
            return WorkflowExecutionStatus.NOT_FOUND;
        }
        return execution.getStatus();
    }

    /**
     * 执行图
     */
    private WorkflowResult executeGraph(WorkflowExecution execution, VariablePool variablePool) {
        WorkflowDefinition definition = execution.getDefinition();
        Map<String, NodeDefinition> nodes = definition.getNodes();
        Map<String, List<EdgeDefinition>> edges = definition.getEdges();

        // 查找开始节点
        NodeDefinition startNode = findStartNode(nodes);
        if (startNode == null) {
            throw new DifyException(ResultCode.WORKFLOW_CONFIG_ERROR, "未找到开始节点");
        }

        // 执行节点
        Set<String> visited = new HashSet<>();
        return executeNode(execution, startNode, nodes, edges, variablePool, visited);
    }

    /**
     * 执行节点
     */
    private WorkflowResult executeNode(WorkflowExecution execution, NodeDefinition nodeDefinition,
                                     Map<String, NodeDefinition> allNodes, Map<String, List<EdgeDefinition>> edges,
                                     VariablePool variablePool, Set<String> visited) {
        
        String nodeId = nodeDefinition.getId();
        log.debug("执行节点: nodeId={}, type={}", nodeId, nodeDefinition.getType());

        // 检查是否已停止
        if (execution.isStopped()) {
            return WorkflowResult.stopped(execution.getId());
        }

        // 防止循环
        if (visited.contains(nodeId)) {
            log.warn("检测到循环依赖: nodeId={}", nodeId);
            return WorkflowResult.error(execution.getId(), "检测到循环依赖");
        }
        visited.add(nodeId);

        try {
            // 创建节点实例
            Node node = nodeFactory.createNode(nodeDefinition);
            
            // 执行节点
            NodeResult nodeResult = node.execute(variablePool);
            
            // 更新变量池
            if (nodeResult.getOutputs() != null) {
                variablePool.setNodeOutputs(nodeId, nodeResult.getOutputs());
            }

            // 如果是结束节点或执行失败，返回结果
            if ("end".equals(nodeDefinition.getType()) || !nodeResult.isSuccess()) {
                return createWorkflowResult(execution.getId(), nodeResult, variablePool);
            }

            // 查找下一个节点
            List<EdgeDefinition> nodeEdges = edges.get(nodeId);
            if (nodeEdges == null || nodeEdges.isEmpty()) {
                // 没有后续节点，工作流结束
                return WorkflowResult.success(execution.getId(), variablePool.getOutputs());
            }

            // 执行下一个节点
            for (EdgeDefinition edge : nodeEdges) {
                // 检查边的条件
                if (evaluateEdgeCondition(edge, variablePool)) {
                    NodeDefinition nextNode = allNodes.get(edge.getTargetNodeId());
                    if (nextNode != null) {
                        return executeNode(execution, nextNode, allNodes, edges, variablePool, new HashSet<>(visited));
                    }
                }
            }

            // 没有满足条件的边，工作流结束
            return WorkflowResult.success(execution.getId(), variablePool.getOutputs());

        } catch (Exception e) {
            log.error("节点执行失败: nodeId={}", nodeId, e);
            return WorkflowResult.error(execution.getId(), "节点执行失败: " + e.getMessage());
        }
    }

    /**
     * 查找开始节点
     */
    private NodeDefinition findStartNode(Map<String, NodeDefinition> nodes) {
        return nodes.values().stream()
                .filter(node -> "start".equals(node.getType()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 评估边的条件
     */
    private boolean evaluateEdgeCondition(EdgeDefinition edge, VariablePool variablePool) {
        // 如果没有条件，默认为true
        if (edge.getCondition() == null || edge.getCondition().isEmpty()) {
            return true;
        }

        // TODO: 实现条件表达式评估
        // 这里可以使用SpEL或其他表达式引擎
        return true;
    }

    /**
     * 创建工作流结果
     */
    private WorkflowResult createWorkflowResult(String executionId, NodeResult nodeResult, VariablePool variablePool) {
        if (nodeResult.isSuccess()) {
            return WorkflowResult.success(executionId, variablePool.getOutputs());
        } else {
            return WorkflowResult.error(executionId, nodeResult.getError());
        }
    }
}
