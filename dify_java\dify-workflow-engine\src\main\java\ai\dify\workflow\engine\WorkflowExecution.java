package ai.dify.workflow.engine;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 工作流执行实例
 */
@Data
public class WorkflowExecution {

    /**
     * 执行ID
     */
    private String id;

    /**
     * 工作流定义
     */
    private WorkflowDefinition definition;

    /**
     * 执行状态
     */
    private WorkflowExecutionStatus status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否已停止
     */
    private AtomicBoolean stopped = new AtomicBoolean(false);

    /**
     * 当前执行的节点ID
     */
    private String currentNodeId;

    public WorkflowExecution(String id, WorkflowDefinition definition) {
        this.id = id;
        this.definition = definition;
        this.status = WorkflowExecutionStatus.PENDING;
        this.startTime = LocalDateTime.now();
    }

    /**
     * 停止执行
     */
    public void stop() {
        this.stopped.set(true);
        this.status = WorkflowExecutionStatus.STOPPED;
        this.endTime = LocalDateTime.now();
    }

    /**
     * 是否已停止
     */
    public boolean isStopped() {
        return stopped.get();
    }

    /**
     * 设置为运行中
     */
    public void setRunning() {
        this.status = WorkflowExecutionStatus.RUNNING;
    }

    /**
     * 设置为完成
     */
    public void setCompleted() {
        this.status = WorkflowExecutionStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
    }

    /**
     * 设置为失败
     */
    public void setFailed() {
        this.status = WorkflowExecutionStatus.FAILED;
        this.endTime = LocalDateTime.now();
    }

    /**
     * 获取执行耗时（毫秒）
     */
    public Long getDuration() {
        if (startTime == null) {
            return null;
        }
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }
}
