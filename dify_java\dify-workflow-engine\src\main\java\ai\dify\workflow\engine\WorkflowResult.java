package ai.dify.workflow.engine;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 工作流执行结果
 */
@Data
public class WorkflowResult {

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 执行状态
     */
    private WorkflowExecutionStatus status;

    /**
     * 输出结果
     */
    private Map<String, Object> outputs;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long duration;

    /**
     * 创建成功结果
     */
    public static WorkflowResult success(String executionId, Map<String, Object> outputs) {
        WorkflowResult result = new WorkflowResult();
        result.setExecutionId(executionId);
        result.setStatus(WorkflowExecutionStatus.COMPLETED);
        result.setOutputs(outputs);
        result.setEndTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建错误结果
     */
    public static WorkflowResult error(String executionId, String error) {
        WorkflowResult result = new WorkflowResult();
        result.setExecutionId(executionId);
        result.setStatus(WorkflowExecutionStatus.FAILED);
        result.setError(error);
        result.setEndTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建停止结果
     */
    public static WorkflowResult stopped(String executionId) {
        WorkflowResult result = new WorkflowResult();
        result.setExecutionId(executionId);
        result.setStatus(WorkflowExecutionStatus.STOPPED);
        result.setEndTime(LocalDateTime.now());
        return result;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return WorkflowExecutionStatus.COMPLETED.equals(status);
    }

    /**
     * 是否失败
     */
    public boolean isError() {
        return WorkflowExecutionStatus.FAILED.equals(status);
    }

    /**
     * 是否被停止
     */
    public boolean isStopped() {
        return WorkflowExecutionStatus.STOPPED.equals(status);
    }
}
