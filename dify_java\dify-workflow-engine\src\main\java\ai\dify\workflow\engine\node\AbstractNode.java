package ai.dify.workflow.engine.node;

import ai.dify.workflow.engine.NodeDefinition;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 抽象节点基类
 */
@Slf4j
@Data
public abstract class AbstractNode implements Node {

    /**
     * 节点定义
     */
    protected NodeDefinition definition;

    @Override
    public NodeResult execute(VariablePool variablePool) {
        LocalDateTime startTime = LocalDateTime.now();
        log.debug("开始执行节点: id={}, type={}", definition.getId(), definition.getType());

        try {
            // 预处理输入变量
            Map<String, Object> processedInputs = processInputs(variablePool);
            
            // 执行节点逻辑
            NodeResult result = doExecute(processedInputs, variablePool);
            
            // 后处理输出变量
            Map<String, Object> processedOutputs = processOutputs(result.getOutputs(), variablePool);
            result.setOutputs(processedOutputs);
            
            // 设置执行时间
            LocalDateTime endTime = LocalDateTime.now();
            result.setExecutionTime(startTime, endTime);
            
            log.debug("节点执行完成: id={}, success={}, duration={}ms", 
                definition.getId(), result.isSuccess(), result.getDuration());
            
            return result;
            
        } catch (Exception e) {
            log.error("节点执行失败: id={}, type={}", definition.getId(), definition.getType(), e);
            
            NodeResult result = NodeResult.error(e);
            result.setExecutionTime(startTime, LocalDateTime.now());
            return result;
        }
    }

    /**
     * 子类实现具体的执行逻辑
     */
    protected abstract NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool);

    /**
     * 预处理输入变量
     */
    protected Map<String, Object> processInputs(VariablePool variablePool) {
        Map<String, Object> inputs = new HashMap<>();
        Map<String, String> inputMappings = definition.getInputMappings();
        
        if (inputMappings != null) {
            for (Map.Entry<String, String> entry : inputMappings.entrySet()) {
                String inputKey = entry.getKey();
                String variableReference = entry.getValue();
                
                Object value = variablePool.getVariableValue(variableReference);
                inputs.put(inputKey, value);
            }
        }
        
        return inputs;
    }

    /**
     * 后处理输出变量
     */
    protected Map<String, Object> processOutputs(Map<String, Object> outputs, VariablePool variablePool) {
        Map<String, Object> processedOutputs = new HashMap<>();
        Map<String, String> outputMappings = definition.getOutputMappings();
        
        if (outputMappings != null && outputs != null) {
            for (Map.Entry<String, String> entry : outputMappings.entrySet()) {
                String outputKey = entry.getKey();
                String targetVariable = entry.getValue();
                
                Object value = outputs.get(outputKey);
                if (value != null) {
                    processedOutputs.put(targetVariable, value);
                }
            }
        } else {
            // 如果没有输出映射，直接返回原始输出
            processedOutputs.putAll(outputs != null ? outputs : new HashMap<>());
        }
        
        return processedOutputs;
    }

    /**
     * 获取配置值
     */
    protected Object getConfigValue(String key) {
        Map<String, Object> config = definition.getConfig();
        return config != null ? config.get(key) : null;
    }

    /**
     * 获取配置值（带默认值）
     */
    protected Object getConfigValue(String key, Object defaultValue) {
        Object value = getConfigValue(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取字符串配置值
     */
    protected String getConfigString(String key) {
        Object value = getConfigValue(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取字符串配置值（带默认值）
     */
    protected String getConfigString(String key, String defaultValue) {
        String value = getConfigString(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取整数配置值
     */
    protected Integer getConfigInteger(String key) {
        Object value = getConfigValue(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取整数配置值（带默认值）
     */
    protected Integer getConfigInteger(String key, Integer defaultValue) {
        Integer value = getConfigInteger(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取布尔配置值
     */
    protected Boolean getConfigBoolean(String key) {
        Object value = getConfigValue(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }

    /**
     * 获取布尔配置值（带默认值）
     */
    protected Boolean getConfigBoolean(String key, Boolean defaultValue) {
        Boolean value = getConfigBoolean(key);
        return value != null ? value : defaultValue;
    }

    @Override
    public String getType() {
        return definition.getType();
    }

    @Override
    public boolean validateConfig() {
        // 基础验证：检查必需的配置项
        return definition != null && definition.getType() != null;
    }
}
