package ai.dify.workflow.engine.node;

import ai.dify.workflow.engine.NodeDefinition;
import ai.dify.workflow.engine.variable.VariablePool;

/**
 * 工作流节点接口
 */
public interface Node {

    /**
     * 执行节点
     * 
     * @param variablePool 变量池
     * @return 节点执行结果
     */
    NodeResult execute(VariablePool variablePool);

    /**
     * 获取节点定义
     */
    NodeDefinition getDefinition();

    /**
     * 获取节点类型
     */
    String getType();

    /**
     * 验证节点配置
     */
    default boolean validateConfig() {
        return true;
    }

    /**
     * 获取节点描述
     */
    default String getDescription() {
        return "Node: " + getType();
    }
}
