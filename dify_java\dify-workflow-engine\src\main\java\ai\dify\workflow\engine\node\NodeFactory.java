package ai.dify.workflow.engine.node;

import ai.dify.common.exception.DifyException;
import ai.dify.common.result.ResultCode;
import ai.dify.workflow.engine.NodeDefinition;
import ai.dify.workflow.engine.node.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 节点工厂 - 根据节点类型创建节点实例
 */
@Slf4j
@Component
public class NodeFactory {

    private final Map<String, Class<? extends Node>> nodeTypes = new HashMap<>();

    public NodeFactory() {
        // 注册内置节点类型
        registerNodeType("start", StartNode.class);
        registerNodeType("end", EndNode.class);
        registerNodeType("llm", LLMNode.class);
        registerNodeType("knowledge_retrieval", KnowledgeRetrievalNode.class);
        registerNodeType("code", CodeNode.class);
        registerNodeType("http_request", HttpRequestNode.class);
        registerNodeType("condition", ConditionNode.class);
        registerNodeType("variable_assigner", VariableAssignerNode.class);
        registerNodeType("template_transform", TemplateTransformNode.class);
    }

    /**
     * 注册节点类型
     */
    public void registerNodeType(String type, Class<? extends Node> nodeClass) {
        nodeTypes.put(type, nodeClass);
        log.debug("注册节点类型: {} -> {}", type, nodeClass.getSimpleName());
    }

    /**
     * 创建节点实例
     */
    public Node createNode(NodeDefinition definition) {
        String type = definition.getType();
        Class<? extends Node> nodeClass = nodeTypes.get(type);
        
        if (nodeClass == null) {
            throw new DifyException(ResultCode.WORKFLOW_CONFIG_ERROR, "不支持的节点类型: " + type);
        }

        try {
            // 创建节点实例
            Node node = nodeClass.getDeclaredConstructor().newInstance();
            
            // 如果是AbstractNode的子类，设置定义
            if (node instanceof AbstractNode) {
                ((AbstractNode) node).setDefinition(definition);
            }
            
            // 验证节点配置
            if (!node.validateConfig()) {
                throw new DifyException(ResultCode.WORKFLOW_CONFIG_ERROR, 
                    "节点配置验证失败: " + definition.getId());
            }
            
            log.debug("创建节点实例: type={}, id={}", type, definition.getId());
            return node;
            
        } catch (Exception e) {
            log.error("创建节点实例失败: type={}, id={}", type, definition.getId(), e);
            throw new DifyException(ResultCode.WORKFLOW_CONFIG_ERROR, 
                "创建节点实例失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的节点类型
     */
    public Map<String, Class<? extends Node>> getSupportedNodeTypes() {
        return new HashMap<>(nodeTypes);
    }

    /**
     * 检查是否支持指定的节点类型
     */
    public boolean isNodeTypeSupported(String type) {
        return nodeTypes.containsKey(type);
    }
}
