package ai.dify.workflow.engine.node;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 节点执行结果
 */
@Data
public class NodeResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 输出数据
     */
    private Map<String, Object> outputs = new HashMap<>();

    /**
     * 错误信息
     */
    private String error;

    /**
     * 执行开始时间
     */
    private LocalDateTime startTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时（毫秒）
     */
    private Long duration;

    /**
     * 元数据
     */
    private Map<String, Object> metadata = new HashMap<>();

    /**
     * 创建成功结果
     */
    public static NodeResult success() {
        NodeResult result = new NodeResult();
        result.setSuccess(true);
        result.setEndTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建成功结果（带输出）
     */
    public static NodeResult success(Map<String, Object> outputs) {
        NodeResult result = success();
        if (outputs != null) {
            result.setOutputs(outputs);
        }
        return result;
    }

    /**
     * 创建成功结果（带单个输出）
     */
    public static NodeResult success(String key, Object value) {
        NodeResult result = success();
        result.addOutput(key, value);
        return result;
    }

    /**
     * 创建失败结果
     */
    public static NodeResult error(String error) {
        NodeResult result = new NodeResult();
        result.setSuccess(false);
        result.setError(error);
        result.setEndTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果（带异常）
     */
    public static NodeResult error(Throwable throwable) {
        return error(throwable.getMessage());
    }

    /**
     * 添加输出
     */
    public NodeResult addOutput(String key, Object value) {
        this.outputs.put(key, value);
        return this;
    }

    /**
     * 添加多个输出
     */
    public NodeResult addOutputs(Map<String, Object> outputs) {
        if (outputs != null) {
            this.outputs.putAll(outputs);
        }
        return this;
    }

    /**
     * 获取输出
     */
    public Object getOutput(String key) {
        return outputs.get(key);
    }

    /**
     * 添加元数据
     */
    public NodeResult addMetadata(String key, Object value) {
        this.metadata.put(key, value);
        return this;
    }

    /**
     * 获取元数据
     */
    public Object getMetadata(String key) {
        return metadata.get(key);
    }

    /**
     * 设置执行时间
     */
    public NodeResult setExecutionTime(LocalDateTime startTime, LocalDateTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        if (startTime != null && endTime != null) {
            this.duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
        return this;
    }

    /**
     * 计算并设置执行耗时
     */
    public NodeResult calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
        return this;
    }
}
