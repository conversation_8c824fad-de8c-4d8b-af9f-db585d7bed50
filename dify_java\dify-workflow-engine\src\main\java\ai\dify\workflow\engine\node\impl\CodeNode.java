package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.HashMap;
import java.util.Map;

/**
 * 代码节点 - 执行JavaScript代码
 */
@Slf4j
public class CodeNode extends AbstractNode {

    private static final ScriptEngineManager scriptEngineManager = new ScriptEngineManager();

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行代码节点: {}", definition.getId());
        
        try {
            // 获取配置
            String code = getConfigString("code", "");
            String language = getConfigString("language", "javascript");
            
            if (code.trim().isEmpty()) {
                return NodeResult.error("代码不能为空");
            }
            
            // 目前只支持JavaScript
            if (!"javascript".equalsIgnoreCase(language)) {
                return NodeResult.error("暂不支持的语言: " + language);
            }
            
            // 执行JavaScript代码
            Object result = executeJavaScript(code, inputs, variablePool);
            
            Map<String, Object> outputs = new HashMap<>();
            outputs.put("result", result);
            
            return NodeResult.success(outputs)
                    .addMetadata("node_type", "code")
                    .addMetadata("language", language);
                    
        } catch (Exception e) {
            log.error("代码节点执行失败: {}", definition.getId(), e);
            return NodeResult.error("代码执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行JavaScript代码
     */
    private Object executeJavaScript(String code, Map<String, Object> inputs, VariablePool variablePool) 
            throws ScriptException {
        
        ScriptEngine engine = scriptEngineManager.getEngineByName("graal.js");
        if (engine == null) {
            // 回退到Nashorn（如果可用）
            engine = scriptEngineManager.getEngineByName("nashorn");
        }
        if (engine == null) {
            throw new RuntimeException("JavaScript引擎不可用");
        }
        
        // 设置输入变量
        engine.put("inputs", inputs);
        engine.put("variables", variablePool.getAllVariables());
        
        // 提供一些工具函数
        engine.eval("""
            var console = {
                log: function(msg) {
                    java.lang.System.out.println('[CodeNode] ' + msg);
                }
            };
            
            var JSON = {
                stringify: function(obj) {
                    return java.lang.String.valueOf(obj);
                },
                parse: function(str) {
                    return eval('(' + str + ')');
                }
            };
            """);
        
        // 执行用户代码
        return engine.eval(code);
    }

    @Override
    public boolean validateConfig() {
        if (!super.validateConfig()) {
            return false;
        }
        
        // 验证必需的配置
        String code = getConfigString("code");
        return code != null && !code.trim().isEmpty();
    }

    @Override
    public String getDescription() {
        return "代码执行节点 - 执行JavaScript代码";
    }
}
