package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 条件节点 - 根据条件进行分支判断
 */
@Slf4j
public class ConditionNode extends AbstractNode {

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行条件节点: {}", definition.getId());
        
        try {
            // 获取条件配置
            List<Map<String, Object>> conditions = getConfigConditions();
            
            if (conditions == null || conditions.isEmpty()) {
                return NodeResult.error("条件配置不能为空");
            }
            
            // 评估条件
            for (int i = 0; i < conditions.size(); i++) {
                Map<String, Object> condition = conditions.get(i);
                boolean result = evaluateCondition(condition, variablePool);
                
                if (result) {
                    Map<String, Object> outputs = new HashMap<>();
                    outputs.put("result", true);
                    outputs.put("matched_condition", i);
                    outputs.put("branch", "condition_" + i);
                    
                    return NodeResult.success(outputs)
                            .addMetadata("node_type", "condition")
                            .addMetadata("matched_condition", i);
                }
            }
            
            // 没有匹配的条件，走默认分支
            Map<String, Object> outputs = new HashMap<>();
            outputs.put("result", false);
            outputs.put("matched_condition", -1);
            outputs.put("branch", "default");
            
            return NodeResult.success(outputs)
                    .addMetadata("node_type", "condition")
                    .addMetadata("matched_condition", -1);
                    
        } catch (Exception e) {
            log.error("条件节点执行失败: {}", definition.getId(), e);
            return NodeResult.error("条件评估失败: " + e.getMessage());
        }
    }

    /**
     * 获取条件配置
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getConfigConditions() {
        Object conditionsConfig = getConfigValue("conditions");
        if (conditionsConfig instanceof List) {
            return (List<Map<String, Object>>) conditionsConfig;
        }
        return null;
    }

    /**
     * 评估单个条件
     */
    private boolean evaluateCondition(Map<String, Object> condition, VariablePool variablePool) {
        String variable = (String) condition.get("variable");
        String operator = (String) condition.get("operator");
        Object value = condition.get("value");
        
        if (variable == null || operator == null) {
            return false;
        }
        
        // 获取变量值
        Object variableValue = variablePool.getVariableValue(variable);
        
        // 根据操作符进行比较
        return switch (operator.toLowerCase()) {
            case "equals", "==" -> equals(variableValue, value);
            case "not_equals", "!=" -> !equals(variableValue, value);
            case "greater_than", ">" -> compare(variableValue, value) > 0;
            case "greater_than_or_equal", ">=" -> compare(variableValue, value) >= 0;
            case "less_than", "<" -> compare(variableValue, value) < 0;
            case "less_than_or_equal", "<=" -> compare(variableValue, value) <= 0;
            case "contains" -> contains(variableValue, value);
            case "not_contains" -> !contains(variableValue, value);
            case "starts_with" -> startsWith(variableValue, value);
            case "ends_with" -> endsWith(variableValue, value);
            case "is_empty" -> isEmpty(variableValue);
            case "is_not_empty" -> !isEmpty(variableValue);
            default -> {
                log.warn("不支持的操作符: {}", operator);
                yield false;
            }
        };
    }

    /**
     * 相等比较
     */
    private boolean equals(Object a, Object b) {
        if (a == null && b == null) return true;
        if (a == null || b == null) return false;
        return a.toString().equals(b.toString());
    }

    /**
     * 数值比较
     */
    private int compare(Object a, Object b) {
        try {
            double numA = Double.parseDouble(a.toString());
            double numB = Double.parseDouble(b.toString());
            return Double.compare(numA, numB);
        } catch (NumberFormatException e) {
            // 如果不是数字，按字符串比较
            return a.toString().compareTo(b.toString());
        }
    }

    /**
     * 包含检查
     */
    private boolean contains(Object a, Object b) {
        if (a == null || b == null) return false;
        return a.toString().contains(b.toString());
    }

    /**
     * 开头检查
     */
    private boolean startsWith(Object a, Object b) {
        if (a == null || b == null) return false;
        return a.toString().startsWith(b.toString());
    }

    /**
     * 结尾检查
     */
    private boolean endsWith(Object a, Object b) {
        if (a == null || b == null) return false;
        return a.toString().endsWith(b.toString());
    }

    /**
     * 空值检查
     */
    private boolean isEmpty(Object a) {
        if (a == null) return true;
        String str = a.toString().trim();
        return str.isEmpty();
    }

    @Override
    public boolean validateConfig() {
        if (!super.validateConfig()) {
            return false;
        }
        
        // 验证条件配置
        List<Map<String, Object>> conditions = getConfigConditions();
        return conditions != null && !conditions.isEmpty();
    }

    @Override
    public String getDescription() {
        return "条件节点 - 根据条件进行分支判断";
    }
}
