package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 结束节点
 */
@Slf4j
public class EndNode extends AbstractNode {

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行结束节点: {}", definition.getId());
        
        // 结束节点用于标记工作流结束
        // 可以收集最终输出或执行清理逻辑
        
        // 获取最终输出配置
        Object outputConfig = getConfigValue("outputs");
        if (outputConfig instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> outputs = (Map<String, Object>) outputConfig;
            
            // 解析输出变量引用
            for (Map.Entry<String, Object> entry : outputs.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (value instanceof String) {
                    // 解析变量引用
                    Object resolvedValue = variablePool.getVariableValue((String) value);
                    variablePool.setOutput(key, resolvedValue);
                } else {
                    variablePool.setOutput(key, value);
                }
            }
        }
        
        return NodeResult.success()
                .addOutput("finished", true)
                .addOutput("end_time", System.currentTimeMillis())
                .addMetadata("node_type", "end");
    }

    @Override
    public String getDescription() {
        return "工作流结束节点";
    }
}
