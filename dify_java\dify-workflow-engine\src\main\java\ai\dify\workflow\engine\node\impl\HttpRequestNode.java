package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * HTTP请求节点 - 发送HTTP请求
 */
@Slf4j
public class HttpRequestNode extends AbstractNode {

    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build();

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行HTTP请求节点: {}", definition.getId());
        
        try {
            // 获取配置
            String method = getConfigString("method", "GET").toUpperCase();
            String url = getConfigString("url", "");
            Map<String, String> headers = getConfigHeaders();
            String body = getConfigString("body", "");
            Integer timeout = getConfigInteger("timeout", 30);
            
            if (url.trim().isEmpty()) {
                return NodeResult.error("URL不能为空");
            }
            
            // 解析URL和body中的变量引用
            String resolvedUrl = variablePool.resolveVariableReferences(url);
            String resolvedBody = variablePool.resolveVariableReferences(body);
            
            // 构建请求
            Request.Builder requestBuilder = new Request.Builder().url(resolvedUrl);
            
            // 添加请求头
            if (headers != null) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    String headerValue = variablePool.resolveVariableReferences(header.getValue());
                    requestBuilder.addHeader(header.getKey(), headerValue);
                }
            }
            
            // 设置请求体
            if ("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method)) {
                MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
                RequestBody requestBody = RequestBody.create(resolvedBody, mediaType);
                requestBuilder.method(method, requestBody);
            } else {
                requestBuilder.method(method, null);
            }
            
            Request request = requestBuilder.build();
            
            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                
                Map<String, Object> outputs = new HashMap<>();
                outputs.put("status_code", response.code());
                outputs.put("headers", response.headers().toMultimap());
                outputs.put("body", responseBody);
                outputs.put("success", response.isSuccessful());
                
                return NodeResult.success(outputs)
                        .addMetadata("node_type", "http_request")
                        .addMetadata("method", method)
                        .addMetadata("url", resolvedUrl);
            }
            
        } catch (IOException e) {
            log.error("HTTP请求节点执行失败: {}", definition.getId(), e);
            return NodeResult.error("HTTP请求失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("HTTP请求节点执行失败: {}", definition.getId(), e);
            return NodeResult.error("HTTP请求节点执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取请求头配置
     */
    @SuppressWarnings("unchecked")
    private Map<String, String> getConfigHeaders() {
        Object headersConfig = getConfigValue("headers");
        if (headersConfig instanceof Map) {
            return (Map<String, String>) headersConfig;
        }
        return new HashMap<>();
    }

    @Override
    public boolean validateConfig() {
        if (!super.validateConfig()) {
            return false;
        }
        
        // 验证必需的配置
        String url = getConfigString("url");
        String method = getConfigString("method", "GET");
        
        return url != null && !url.trim().isEmpty() && 
               ("GET".equalsIgnoreCase(method) || "POST".equalsIgnoreCase(method) || 
                "PUT".equalsIgnoreCase(method) || "DELETE".equalsIgnoreCase(method) ||
                "PATCH".equalsIgnoreCase(method));
    }

    @Override
    public String getDescription() {
        return "HTTP请求节点 - 发送HTTP请求到外部服务";
    }
}
