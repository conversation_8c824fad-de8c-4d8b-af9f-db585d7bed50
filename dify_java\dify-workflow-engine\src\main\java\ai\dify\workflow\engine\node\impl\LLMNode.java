package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * LLM节点 - 调用大语言模型
 */
@Slf4j
public class LLMNode extends AbstractNode {

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行LLM节点: {}", definition.getId());
        
        try {
            // 获取配置
            String model = getConfigString("model", "gpt-3.5-turbo");
            String prompt = getConfigString("prompt", "");
            Integer maxTokens = getConfigInteger("max_tokens", 1000);
            Double temperature = getConfigDouble("temperature", 0.7);
            
            // 解析提示词中的变量引用
            String resolvedPrompt = variablePool.resolveVariableReferences(prompt);
            
            // TODO: 实际调用LLM服务
            // 这里先返回模拟结果
            String response = simulateLLMCall(resolvedPrompt, model, maxTokens, temperature);
            
            Map<String, Object> outputs = new HashMap<>();
            outputs.put("text", response);
            outputs.put("model", model);
            outputs.put("prompt_tokens", resolvedPrompt.length() / 4); // 简单估算
            outputs.put("completion_tokens", response.length() / 4);
            outputs.put("total_tokens", (resolvedPrompt.length() + response.length()) / 4);
            
            return NodeResult.success(outputs)
                    .addMetadata("node_type", "llm")
                    .addMetadata("model", model);
                    
        } catch (Exception e) {
            log.error("LLM节点执行失败: {}", definition.getId(), e);
            return NodeResult.error("LLM调用失败: " + e.getMessage());
        }
    }

    /**
     * 模拟LLM调用
     */
    private String simulateLLMCall(String prompt, String model, Integer maxTokens, Double temperature) {
        // 这里是模拟实现，实际应该调用真实的LLM API
        log.info("模拟LLM调用: model={}, prompt={}, maxTokens={}, temperature={}", 
                model, prompt.substring(0, Math.min(50, prompt.length())), maxTokens, temperature);
        
        return "这是一个模拟的LLM响应，基于输入: " + prompt.substring(0, Math.min(100, prompt.length()));
    }

    /**
     * 获取Double配置值
     */
    private Double getConfigDouble(String key, Double defaultValue) {
        Object value = getConfigValue(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    @Override
    public boolean validateConfig() {
        if (!super.validateConfig()) {
            return false;
        }
        
        // 验证必需的配置
        String prompt = getConfigString("prompt");
        return prompt != null && !prompt.trim().isEmpty();
    }

    @Override
    public String getDescription() {
        return "大语言模型节点 - 调用LLM生成文本";
    }
}
