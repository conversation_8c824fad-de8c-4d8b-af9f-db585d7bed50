package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.context.WorkflowContext;
import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeDefinition;
import ai.dify.workflow.engine.node.NodeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 循环节点
 */
@Slf4j
@Component
public class LoopNode extends AbstractNode {

    @Override
    public String getType() {
        return "loop";
    }

    @Override
    protected NodeResult doExecute(NodeDefinition definition, WorkflowContext context) {
        log.debug("执行循环节点: {}", definition.getId());
        
        try {
            // 获取循环配置
            String loopType = getStringConfig(definition, "loop_type", "count");
            
            switch (loopType) {
                case "count":
                    return executeCountLoop(definition, context);
                case "array":
                    return executeArrayLoop(definition, context);
                case "condition":
                    return executeConditionLoop(definition, context);
                default:
                    return NodeResult.failure("不支持的循环类型: " + loopType);
            }
            
        } catch (Exception e) {
            log.error("循环节点执行失败", e);
            return NodeResult.failure("循环节点执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行计数循环
     */
    private NodeResult executeCountLoop(NodeDefinition definition, WorkflowContext context) {
        Integer count = getIntConfig(definition, "count", 1);
        Integer maxIterations = getIntConfig(definition, "max_iterations", 100);
        
        if (count > maxIterations) {
            return NodeResult.failure("循环次数超过最大限制: " + maxIterations);
        }
        
        List<Object> results = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            // 设置循环变量
            context.setVariable("loop_index", i);
            context.setVariable("loop_count", count);
            
            // 执行循环体
            NodeResult iterationResult = executeLoopBody(definition, context, i);
            
            if (iterationResult.isSuccess()) {
                results.add(iterationResult.getOutputs());
            } else {
                // 检查是否继续执行
                Boolean continueOnError = getBooleanConfig(definition, "continue_on_error", false);
                if (!continueOnError) {
                    return NodeResult.failure("循环在第" + (i + 1) + "次迭代时失败: " + iterationResult.getError());
                }
                results.add(Map.of("error", iterationResult.getError()));
            }
        }
        
        return NodeResult.success(Map.of("results", results, "total_iterations", count));
    }

    /**
     * 执行数组循环
     */
    private NodeResult executeArrayLoop(NodeDefinition definition, WorkflowContext context) {
        String arrayVariable = getStringConfig(definition, "array_variable");
        if (arrayVariable == null) {
            return NodeResult.failure("数组变量名不能为空");
        }
        
        Object arrayValue = context.getVariable(arrayVariable);
        if (!(arrayValue instanceof List)) {
            return NodeResult.failure("变量不是数组类型: " + arrayVariable);
        }
        
        @SuppressWarnings("unchecked")
        List<Object> array = (List<Object>) arrayValue;
        
        Integer maxIterations = getIntConfig(definition, "max_iterations", 100);
        if (array.size() > maxIterations) {
            return NodeResult.failure("数组长度超过最大限制: " + maxIterations);
        }
        
        List<Object> results = new ArrayList<>();
        
        for (int i = 0; i < array.size(); i++) {
            Object item = array.get(i);
            
            // 设置循环变量
            context.setVariable("loop_index", i);
            context.setVariable("loop_item", item);
            context.setVariable("loop_count", array.size());
            
            // 执行循环体
            NodeResult iterationResult = executeLoopBody(definition, context, i);
            
            if (iterationResult.isSuccess()) {
                results.add(iterationResult.getOutputs());
            } else {
                Boolean continueOnError = getBooleanConfig(definition, "continue_on_error", false);
                if (!continueOnError) {
                    return NodeResult.failure("循环在第" + (i + 1) + "次迭代时失败: " + iterationResult.getError());
                }
                results.add(Map.of("error", iterationResult.getError()));
            }
        }
        
        return NodeResult.success(Map.of("results", results, "total_iterations", array.size()));
    }

    /**
     * 执行条件循环
     */
    private NodeResult executeConditionLoop(NodeDefinition definition, WorkflowContext context) {
        String condition = getStringConfig(definition, "condition");
        if (condition == null) {
            return NodeResult.failure("循环条件不能为空");
        }
        
        Integer maxIterations = getIntConfig(definition, "max_iterations", 100);
        List<Object> results = new ArrayList<>();
        
        int iteration = 0;
        while (iteration < maxIterations) {
            // 设置循环变量
            context.setVariable("loop_index", iteration);
            
            // 评估条件
            boolean conditionResult = evaluateCondition(condition, context);
            if (!conditionResult) {
                break;
            }
            
            // 执行循环体
            NodeResult iterationResult = executeLoopBody(definition, context, iteration);
            
            if (iterationResult.isSuccess()) {
                results.add(iterationResult.getOutputs());
            } else {
                Boolean continueOnError = getBooleanConfig(definition, "continue_on_error", false);
                if (!continueOnError) {
                    return NodeResult.failure("循环在第" + (iteration + 1) + "次迭代时失败: " + iterationResult.getError());
                }
                results.add(Map.of("error", iterationResult.getError()));
            }
            
            iteration++;
        }
        
        if (iteration >= maxIterations) {
            log.warn("循环达到最大迭代次数限制: {}", maxIterations);
        }
        
        return NodeResult.success(Map.of("results", results, "total_iterations", iteration));
    }

    /**
     * 执行循环体
     */
    private NodeResult executeLoopBody(NodeDefinition definition, WorkflowContext context, int iteration) {
        // TODO: 这里需要执行循环体中的子节点
        // 实际实现中需要获取循环体的节点定义并执行
        
        // 模拟循环体执行
        return NodeResult.success(Map.of("iteration", iteration, "timestamp", System.currentTimeMillis()));
    }

    /**
     * 评估条件表达式
     */
    private boolean evaluateCondition(String condition, WorkflowContext context) {
        // TODO: 实现条件表达式评估
        // 这里应该支持简单的表达式，如 "{{variable}} > 10"
        
        // 简化实现，总是返回false来结束循环
        return false;
    }

    @Override
    public boolean validateDefinition(NodeDefinition definition) {
        String loopType = getStringConfig(definition, "loop_type", "count");
        
        switch (loopType) {
            case "count":
                Integer count = getIntConfig(definition, "count");
                return count != null && count > 0;
            case "array":
                String arrayVariable = getStringConfig(definition, "array_variable");
                return arrayVariable != null && !arrayVariable.trim().isEmpty();
            case "condition":
                String condition = getStringConfig(definition, "condition");
                return condition != null && !condition.trim().isEmpty();
            default:
                return false;
        }
    }
}
