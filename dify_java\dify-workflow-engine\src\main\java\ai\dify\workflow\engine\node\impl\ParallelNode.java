package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.context.WorkflowContext;
import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeDefinition;
import ai.dify.workflow.engine.node.NodeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 并行执行节点
 */
@Slf4j
@Component
public class ParallelNode extends AbstractNode {

    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Override
    public String getType() {
        return "parallel";
    }

    @Override
    protected NodeResult doExecute(NodeDefinition definition, WorkflowContext context) {
        log.debug("执行并行节点: {}", definition.getId());
        
        try {
            // 获取并行分支
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> branches = (List<Map<String, Object>>) definition.getConfig().get("branches");
            
            if (branches == null || branches.isEmpty()) {
                return NodeResult.failure("并行分支不能为空");
            }
            
            // 获取执行策略
            String strategy = getStringConfig(definition, "strategy", "all");
            Integer timeout = getIntConfig(definition, "timeout", 300); // 默认5分钟超时
            
            // 创建并行任务
            List<CompletableFuture<BranchResult>> futures = new ArrayList<>();
            
            for (int i = 0; i < branches.size(); i++) {
                Map<String, Object> branch = branches.get(i);
                int branchIndex = i;
                
                CompletableFuture<BranchResult> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        // 创建分支上下文（隔离变量）
                        WorkflowContext branchContext = context.createBranch();
                        
                        // 执行分支
                        NodeResult result = executeBranch(branch, branchContext, branchIndex);
                        
                        return new BranchResult(branchIndex, result, null);
                        
                    } catch (Exception e) {
                        log.error("并行分支执行失败: branch={}", branchIndex, e);
                        return new BranchResult(branchIndex, null, e);
                    }
                }, executorService);
                
                futures.add(future);
            }
            
            // 等待执行完成
            List<BranchResult> results = waitForCompletion(futures, strategy, timeout);
            
            // 处理结果
            return processResults(results, strategy);
            
        } catch (Exception e) {
            log.error("并行节点执行失败", e);
            return NodeResult.failure("并行节点执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行分支
     */
    private NodeResult executeBranch(Map<String, Object> branch, WorkflowContext context, int branchIndex) {
        // TODO: 这里需要执行分支中的节点
        // 实际实现中需要获取分支的节点定义并执行
        
        // 模拟分支执行
        try {
            Thread.sleep(100 + (int)(Math.random() * 1000)); // 模拟执行时间
            
            return NodeResult.success(Map.of(
                "branch_index", branchIndex,
                "result", "Branch " + branchIndex + " completed",
                "timestamp", System.currentTimeMillis()
            ));
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return NodeResult.failure("分支执行被中断");
        }
    }

    /**
     * 等待执行完成
     */
    private List<BranchResult> waitForCompletion(List<CompletableFuture<BranchResult>> futures, 
                                                String strategy, int timeoutSeconds) {
        List<BranchResult> results = new ArrayList<>();
        
        try {
            if ("all".equals(strategy)) {
                // 等待所有分支完成
                CompletableFuture<Void> allOf = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
                );
                
                allOf.get(timeoutSeconds, TimeUnit.SECONDS);
                
                for (CompletableFuture<BranchResult> future : futures) {
                    results.add(future.get());
                }
                
            } else if ("any".equals(strategy)) {
                // 等待任意一个分支完成
                CompletableFuture<Object> anyOf = CompletableFuture.anyOf(
                    futures.toArray(new CompletableFuture[0])
                );
                
                anyOf.get(timeoutSeconds, TimeUnit.SECONDS);
                
                // 收集已完成的结果
                for (CompletableFuture<BranchResult> future : futures) {
                    if (future.isDone() && !future.isCompletedExceptionally()) {
                        results.add(future.get());
                    }
                }
                
                // 取消未完成的任务
                for (CompletableFuture<BranchResult> future : futures) {
                    if (!future.isDone()) {
                        future.cancel(true);
                    }
                }
                
            } else if ("first".equals(strategy)) {
                // 等待第一个成功的分支
                for (CompletableFuture<BranchResult> future : futures) {
                    try {
                        BranchResult result = future.get(timeoutSeconds, TimeUnit.SECONDS);
                        if (result.getResult() != null && result.getResult().isSuccess()) {
                            results.add(result);
                            break;
                        }
                    } catch (Exception e) {
                        // 继续尝试下一个
                    }
                }
                
                // 取消其他任务
                for (CompletableFuture<BranchResult> future : futures) {
                    if (!future.isDone()) {
                        future.cancel(true);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("等待并行执行完成失败", e);
            
            // 取消所有未完成的任务
            for (CompletableFuture<BranchResult> future : futures) {
                if (!future.isDone()) {
                    future.cancel(true);
                }
            }
        }
        
        return results;
    }

    /**
     * 处理结果
     */
    private NodeResult processResults(List<BranchResult> results, String strategy) {
        if (results.isEmpty()) {
            return NodeResult.failure("没有分支成功执行");
        }
        
        List<Object> successResults = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        for (BranchResult result : results) {
            if (result.getException() != null) {
                errors.add("分支" + result.getBranchIndex() + "异常: " + result.getException().getMessage());
            } else if (result.getResult() != null) {
                if (result.getResult().isSuccess()) {
                    successResults.add(result.getResult().getOutputs());
                } else {
                    errors.add("分支" + result.getBranchIndex() + "失败: " + result.getResult().getError());
                }
            }
        }
        
        Map<String, Object> outputs = Map.of(
            "successful_branches", successResults,
            "failed_branches", errors,
            "total_branches", results.size(),
            "strategy", strategy
        );
        
        if ("all".equals(strategy) && !errors.isEmpty()) {
            return NodeResult.failure("部分分支执行失败", outputs);
        }
        
        if (successResults.isEmpty()) {
            return NodeResult.failure("所有分支都执行失败", outputs);
        }
        
        return NodeResult.success(outputs);
    }

    @Override
    public boolean validateDefinition(NodeDefinition definition) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> branches = (List<Map<String, Object>>) definition.getConfig().get("branches");
        
        if (branches == null || branches.isEmpty()) {
            return false;
        }
        
        String strategy = getStringConfig(definition, "strategy", "all");
        if (!List.of("all", "any", "first").contains(strategy)) {
            return false;
        }
        
        return super.validateDefinition(definition);
    }

    /**
     * 分支执行结果
     */
    private static class BranchResult {
        private final int branchIndex;
        private final NodeResult result;
        private final Exception exception;

        public BranchResult(int branchIndex, NodeResult result, Exception exception) {
            this.branchIndex = branchIndex;
            this.result = result;
            this.exception = exception;
        }

        public int getBranchIndex() {
            return branchIndex;
        }

        public NodeResult getResult() {
            return result;
        }

        public Exception getException() {
            return exception;
        }
    }
}
