package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 开始节点
 */
@Slf4j
public class StartNode extends AbstractNode {

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行开始节点: {}", definition.getId());
        
        // 开始节点主要用于初始化工作流
        // 可以设置一些初始变量或执行初始化逻辑
        
        return NodeResult.success()
                .addOutput("started", true)
                .addOutput("start_time", System.currentTimeMillis())
                .addMetadata("node_type", "start");
    }

    @Override
    public String getDescription() {
        return "工作流开始节点";
    }
}
