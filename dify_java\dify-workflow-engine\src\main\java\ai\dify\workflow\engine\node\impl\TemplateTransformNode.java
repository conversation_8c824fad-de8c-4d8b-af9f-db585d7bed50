package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板转换节点 - 使用模板转换文本
 */
@Slf4j
public class TemplateTransformNode extends AbstractNode {

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\{\\{([^}]+)\\}\\}");

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行模板转换节点: {}", definition.getId());
        
        try {
            // 获取配置
            String template = getConfigString("template", "");
            
            if (template.trim().isEmpty()) {
                return NodeResult.error("模板内容不能为空");
            }
            
            // 执行模板转换
            String result = transformTemplate(template, variablePool);
            
            Map<String, Object> outputs = new HashMap<>();
            outputs.put("output", result);
            outputs.put("template", template);
            
            return NodeResult.success(outputs)
                    .addMetadata("node_type", "template_transform")
                    .addMetadata("template_length", template.length())
                    .addMetadata("output_length", result.length());
                    
        } catch (Exception e) {
            log.error("模板转换节点执行失败: {}", definition.getId(), e);
            return NodeResult.error("模板转换失败: " + e.getMessage());
        }
    }

    /**
     * 执行模板转换
     */
    private String transformTemplate(String template, VariablePool variablePool) {
        String result = template;
        
        // 查找并替换所有变量引用
        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String variableReference = matcher.group(0); // 完整的 {{variable}}
            String variablePath = matcher.group(1);      // variable 部分
            
            // 获取变量值
            Object value = variablePool.getVariableValue(variableReference);
            
            // 如果没有找到变量值，尝试直接使用路径
            if (value == null) {
                value = getVariableByPath(variablePath, variablePool);
            }
            
            // 转换为字符串
            String replacement = value != null ? value.toString() : "";
            
            // 转义特殊字符
            replacement = Matcher.quoteReplacement(replacement);
            
            matcher.appendReplacement(sb, replacement);
        }
        matcher.appendTail(sb);
        
        result = sb.toString();
        
        // 执行其他转换函数
        result = applyTransformFunctions(result, variablePool);
        
        return result;
    }

    /**
     * 根据路径获取变量值
     */
    private Object getVariableByPath(String path, VariablePool variablePool) {
        String[] parts = path.split("\\.");
        
        if (parts.length < 2) {
            return null;
        }
        
        String scope = parts[0];
        String key = String.join(".", java.util.Arrays.copyOfRange(parts, 1, parts.length));
        
        return switch (scope) {
            case "input" -> variablePool.getInput(key);
            case "output" -> variablePool.getOutput(key);
            case "system" -> variablePool.getSystemVariable(key);
            case "env" -> variablePool.getEnvironmentVariable(key);
            case "conversation" -> variablePool.getConversationVariable(key);
            default -> variablePool.getNodeOutput(scope, key);
        };
    }

    /**
     * 应用转换函数
     */
    private String applyTransformFunctions(String text, VariablePool variablePool) {
        // 支持一些简单的转换函数
        // 例如: {{variable | upper}} 或 {{variable | lower}}
        
        // 这里可以实现更复杂的模板引擎功能
        // 目前只是简单的变量替换
        
        return text;
    }

    @Override
    public boolean validateConfig() {
        if (!super.validateConfig()) {
            return false;
        }
        
        // 验证模板配置
        String template = getConfigString("template");
        return template != null && !template.trim().isEmpty();
    }

    @Override
    public String getDescription() {
        return "模板转换节点 - 使用模板转换文本";
    }
}
