package ai.dify.workflow.engine.node.impl;

import ai.dify.agent.tool.Tool;
import ai.dify.agent.tool.ToolCall;
import ai.dify.agent.tool.ToolResult;
import ai.dify.workflow.engine.context.WorkflowContext;
import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeDefinition;
import ai.dify.workflow.engine.node.NodeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 工具调用节点
 */
@Slf4j
@Component
public class ToolNode extends AbstractNode {

    @Override
    public String getType() {
        return "tool";
    }

    @Override
    protected NodeResult doExecute(NodeDefinition definition, WorkflowContext context) {
        log.debug("执行工具节点: {}", definition.getId());
        
        try {
            // 获取工具配置
            String toolId = getStringConfig(definition, "tool_id");
            if (toolId == null || toolId.trim().isEmpty()) {
                return NodeResult.failure("工具ID不能为空");
            }
            
            // 获取工具参数
            Map<String, Object> toolParams = getMapConfig(definition, "tool_parameters");
            
            // 解析参数中的变量
            Map<String, Object> resolvedParams = resolveVariables(toolParams, context);
            
            // 获取工具实例
            Tool tool = getToolById(toolId);
            if (tool == null) {
                return NodeResult.failure("未找到工具: " + toolId);
            }
            
            // 检查工具是否可用
            if (!tool.isAvailable()) {
                return NodeResult.failure("工具不可用: " + toolId);
            }
            
            // 创建工具调用
            ToolCall toolCall = new ToolCall(toolId, resolvedParams);
            
            // 验证工具调用
            if (!tool.validateCall(toolCall)) {
                return NodeResult.failure("工具调用参数验证失败");
            }
            
            // 执行工具
            ToolResult toolResult = tool.execute(toolCall);
            
            if (toolResult.isSuccess()) {
                // 构建输出
                Map<String, Object> outputs = Map.of(
                    "result", toolResult.getOutput(),
                    "structured_result", toolResult.getStructuredOutput(),
                    "metadata", toolResult.getMetadata()
                );
                
                return NodeResult.success(outputs);
            } else {
                return NodeResult.failure("工具执行失败: " + toolResult.getError());
            }
            
        } catch (Exception e) {
            log.error("工具节点执行失败", e);
            return NodeResult.failure("工具节点执行异常: " + e.getMessage());
        }
    }

    /**
     * 获取工具实例
     */
    private Tool getToolById(String toolId) {
        // TODO: 从工具管理器获取工具实例
        // 这里应该注入ToolManager或者从Spring容器获取
        return null;
    }

    /**
     * 解析变量
     */
    private Map<String, Object> resolveVariables(Map<String, Object> params, WorkflowContext context) {
        if (params == null) {
            return Map.of();
        }
        
        Map<String, Object> resolved = new java.util.HashMap<>();
        
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            Object value = entry.getValue();
            
            if (value instanceof String) {
                String strValue = (String) value;
                // 解析变量引用，如 {{variable_name}}
                if (strValue.startsWith("{{") && strValue.endsWith("}}")) {
                    String varName = strValue.substring(2, strValue.length() - 2).trim();
                    Object varValue = context.getVariable(varName);
                    resolved.put(entry.getKey(), varValue);
                } else {
                    resolved.put(entry.getKey(), value);
                }
            } else {
                resolved.put(entry.getKey(), value);
            }
        }
        
        return resolved;
    }

    @Override
    public boolean validateDefinition(NodeDefinition definition) {
        // 验证必需的配置
        String toolId = getStringConfig(definition, "tool_id");
        if (toolId == null || toolId.trim().isEmpty()) {
            return false;
        }
        
        return super.validateDefinition(definition);
    }
}
