package ai.dify.workflow.engine.node.impl;

import ai.dify.workflow.engine.node.AbstractNode;
import ai.dify.workflow.engine.node.NodeResult;
import ai.dify.workflow.engine.variable.VariablePool;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 变量赋值节点 - 设置或修改变量值
 */
@Slf4j
public class VariableAssignerNode extends AbstractNode {

    @Override
    protected NodeResult doExecute(Map<String, Object> inputs, VariablePool variablePool) {
        log.debug("执行变量赋值节点: {}", definition.getId());
        
        try {
            // 获取变量赋值配置
            List<Map<String, Object>> assignments = getConfigAssignments();
            
            if (assignments == null || assignments.isEmpty()) {
                return NodeResult.error("变量赋值配置不能为空");
            }
            
            Map<String, Object> outputs = new HashMap<>();
            int assignedCount = 0;
            
            // 执行变量赋值
            for (Map<String, Object> assignment : assignments) {
                String variableName = (String) assignment.get("variable");
                Object value = assignment.get("value");
                String type = (String) assignment.get("type");
                
                if (variableName == null || variableName.trim().isEmpty()) {
                    log.warn("跳过无效的变量赋值: 变量名为空");
                    continue;
                }
                
                // 解析值中的变量引用
                Object resolvedValue = resolveValue(value, type, variablePool);
                
                // 设置变量
                setVariable(variableName, resolvedValue, variablePool);
                
                outputs.put("assigned_" + assignedCount, Map.of(
                        "variable", variableName,
                        "value", resolvedValue,
                        "type", type != null ? type : "auto"
                ));
                
                assignedCount++;
            }
            
            outputs.put("assigned_count", assignedCount);
            
            return NodeResult.success(outputs)
                    .addMetadata("node_type", "variable_assigner")
                    .addMetadata("assigned_count", assignedCount);
                    
        } catch (Exception e) {
            log.error("变量赋值节点执行失败: {}", definition.getId(), e);
            return NodeResult.error("变量赋值失败: " + e.getMessage());
        }
    }

    /**
     * 获取变量赋值配置
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getConfigAssignments() {
        Object assignmentsConfig = getConfigValue("assignments");
        if (assignmentsConfig instanceof List) {
            return (List<Map<String, Object>>) assignmentsConfig;
        }
        return null;
    }

    /**
     * 解析值
     */
    private Object resolveValue(Object value, String type, VariablePool variablePool) {
        if (value == null) {
            return null;
        }
        
        // 如果是字符串，尝试解析变量引用
        if (value instanceof String) {
            String strValue = (String) value;
            Object resolvedValue = variablePool.getVariableValue(strValue);
            
            // 如果没有解析到变量引用，使用原始值
            if (resolvedValue == null || resolvedValue.equals(strValue)) {
                resolvedValue = variablePool.resolveVariableReferences(strValue);
            }
            
            // 根据类型转换
            return convertType(resolvedValue, type);
        }
        
        return convertType(value, type);
    }

    /**
     * 类型转换
     */
    private Object convertType(Object value, String type) {
        if (value == null || type == null || "auto".equals(type)) {
            return value;
        }
        
        String strValue = value.toString();
        
        return switch (type.toLowerCase()) {
            case "string" -> strValue;
            case "number", "integer" -> {
                try {
                    if (strValue.contains(".")) {
                        yield Double.parseDouble(strValue);
                    } else {
                        yield Long.parseLong(strValue);
                    }
                } catch (NumberFormatException e) {
                    yield value;
                }
            }
            case "boolean" -> {
                if ("true".equalsIgnoreCase(strValue) || "1".equals(strValue)) {
                    yield true;
                } else if ("false".equalsIgnoreCase(strValue) || "0".equals(strValue)) {
                    yield false;
                } else {
                    yield value;
                }
            }
            case "array" -> {
                // 简单的数组解析，实际可能需要更复杂的逻辑
                if (strValue.startsWith("[") && strValue.endsWith("]")) {
                    String content = strValue.substring(1, strValue.length() - 1);
                    yield content.split(",");
                } else {
                    yield new String[]{strValue};
                }
            }
            default -> value;
        };
    }

    /**
     * 设置变量
     */
    private void setVariable(String variableName, Object value, VariablePool variablePool) {
        // 根据变量名的格式决定设置到哪个作用域
        if (variableName.startsWith("output.")) {
            String key = variableName.substring(7);
            variablePool.setOutput(key, value);
        } else if (variableName.startsWith("system.")) {
            String key = variableName.substring(7);
            variablePool.setSystemVariable(key, value);
        } else if (variableName.startsWith("conversation.")) {
            String key = variableName.substring(13);
            variablePool.getConversationVariables().put(key, value);
        } else {
            // 默认设置为输出变量
            variablePool.setOutput(variableName, value);
        }
    }

    @Override
    public boolean validateConfig() {
        if (!super.validateConfig()) {
            return false;
        }
        
        // 验证变量赋值配置
        List<Map<String, Object>> assignments = getConfigAssignments();
        return assignments != null && !assignments.isEmpty();
    }

    @Override
    public String getDescription() {
        return "变量赋值节点 - 设置或修改变量值";
    }
}
