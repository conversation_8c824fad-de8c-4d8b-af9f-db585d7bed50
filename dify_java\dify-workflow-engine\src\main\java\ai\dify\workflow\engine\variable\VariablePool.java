package ai.dify.workflow.engine.variable;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 变量池 - 管理工作流执行过程中的变量
 */
@Data
public class VariablePool {

    /**
     * 输入变量
     */
    private Map<String, Object> inputs = new HashMap<>();

    /**
     * 输出变量
     */
    private Map<String, Object> outputs = new HashMap<>();

    /**
     * 系统变量
     */
    private Map<String, Object> systemVariables = new HashMap<>();

    /**
     * 节点输出变量（按节点ID分组）
     */
    private Map<String, Map<String, Object>> nodeOutputs = new ConcurrentHashMap<>();

    /**
     * 环境变量
     */
    private Map<String, Object> environmentVariables = new HashMap<>();

    /**
     * 对话变量
     */
    private Map<String, Object> conversationVariables = new HashMap<>();

    /**
     * 设置输入变量
     */
    public void setInputs(Map<String, Object> inputs) {
        if (inputs != null) {
            this.inputs.putAll(inputs);
        }
    }

    /**
     * 获取输入变量
     */
    public Object getInput(String key) {
        return inputs.get(key);
    }

    /**
     * 设置输入变量
     */
    public void setInput(String key, Object value) {
        inputs.put(key, value);
    }

    /**
     * 设置输出变量
     */
    public void setOutput(String key, Object value) {
        outputs.put(key, value);
    }

    /**
     * 获取输出变量
     */
    public Object getOutput(String key) {
        return outputs.get(key);
    }

    /**
     * 设置系统变量
     */
    public void setSystemVariable(String key, Object value) {
        systemVariables.put(key, value);
    }

    /**
     * 获取系统变量
     */
    public Object getSystemVariable(String key) {
        return systemVariables.get(key);
    }

    /**
     * 设置节点输出
     */
    public void setNodeOutputs(String nodeId, Map<String, Object> outputs) {
        nodeOutputs.put(nodeId, new HashMap<>(outputs));
    }

    /**
     * 获取节点输出
     */
    public Map<String, Object> getNodeOutputs(String nodeId) {
        return nodeOutputs.get(nodeId);
    }

    /**
     * 获取节点输出变量
     */
    public Object getNodeOutput(String nodeId, String key) {
        Map<String, Object> outputs = nodeOutputs.get(nodeId);
        return outputs != null ? outputs.get(key) : null;
    }

    /**
     * 设置环境变量
     */
    public void setEnvironmentVariables(Map<String, Object> environmentVariables) {
        if (environmentVariables != null) {
            this.environmentVariables.putAll(environmentVariables);
        }
    }

    /**
     * 获取环境变量
     */
    public Object getEnvironmentVariable(String key) {
        return environmentVariables.get(key);
    }

    /**
     * 设置对话变量
     */
    public void setConversationVariables(Map<String, Object> conversationVariables) {
        if (conversationVariables != null) {
            this.conversationVariables.putAll(conversationVariables);
        }
    }

    /**
     * 获取对话变量
     */
    public Object getConversationVariable(String key) {
        return conversationVariables.get(key);
    }

    /**
     * 根据变量引用获取值
     * 支持格式：
     * - {{input.key}} - 输入变量
     * - {{output.key}} - 输出变量
     * - {{system.key}} - 系统变量
     * - {{env.key}} - 环境变量
     * - {{conversation.key}} - 对话变量
     * - {{nodeId.key}} - 节点输出变量
     */
    public Object getVariableValue(String reference) {
        if (reference == null || !reference.startsWith("{{") || !reference.endsWith("}}")) {
            return reference;
        }

        String variablePath = reference.substring(2, reference.length() - 2);
        String[] parts = variablePath.split("\\.", 2);
        
        if (parts.length != 2) {
            return null;
        }

        String scope = parts[0];
        String key = parts[1];

        switch (scope) {
            case "input":
                return getInput(key);
            case "output":
                return getOutput(key);
            case "system":
                return getSystemVariable(key);
            case "env":
                return getEnvironmentVariable(key);
            case "conversation":
                return getConversationVariable(key);
            default:
                // 尝试作为节点ID
                return getNodeOutput(scope, key);
        }
    }

    /**
     * 解析变量引用并替换为实际值
     */
    public String resolveVariableReferences(String text) {
        if (text == null) {
            return null;
        }

        // 简单的变量替换实现
        // TODO: 可以使用更复杂的模板引擎如Freemarker或Velocity
        String result = text;
        
        // 查找所有 {{...}} 模式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\{\\{([^}]+)\\}\\}");
        java.util.regex.Matcher matcher = pattern.matcher(text);
        
        while (matcher.find()) {
            String reference = matcher.group(0);
            Object value = getVariableValue(reference);
            if (value != null) {
                result = result.replace(reference, value.toString());
            }
        }
        
        return result;
    }

    /**
     * 获取所有变量的快照
     */
    public Map<String, Object> getAllVariables() {
        Map<String, Object> allVariables = new HashMap<>();
        allVariables.put("inputs", inputs);
        allVariables.put("outputs", outputs);
        allVariables.put("system", systemVariables);
        allVariables.put("environment", environmentVariables);
        allVariables.put("conversation", conversationVariables);
        allVariables.put("nodes", nodeOutputs);
        return allVariables;
    }
}
