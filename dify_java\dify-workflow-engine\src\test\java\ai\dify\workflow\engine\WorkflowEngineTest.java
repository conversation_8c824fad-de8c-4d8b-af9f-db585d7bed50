package ai.dify.workflow.engine;

import ai.dify.workflow.engine.node.NodeFactory;
import ai.dify.workflow.engine.node.impl.StartNode;
import ai.dify.workflow.engine.node.impl.EndNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 工作流引擎测试
 */
@ExtendWith(MockitoExtension.class)
class WorkflowEngineTest {

    @Mock
    private NodeFactory nodeFactory;

    @InjectMocks
    private WorkflowEngine workflowEngine;

    private WorkflowDefinition workflowDefinition;
    private Map<String, Object> inputs;

    @BeforeEach
    void setUp() {
        // 创建简单的工作流定义
        workflowDefinition = new WorkflowDefinition();
        workflowDefinition.setId("workflow-123");
        workflowDefinition.setName("Test Workflow");
        workflowDefinition.setVersion("1.0");

        // 创建节点定义
        Map<String, NodeDefinition> nodes = new HashMap<>();
        
        // 开始节点
        NodeDefinition startNode = new NodeDefinition();
        startNode.setId("start-1");
        startNode.setName("Start");
        startNode.setType("start");
        nodes.put("start-1", startNode);

        // 结束节点
        NodeDefinition endNode = new NodeDefinition();
        endNode.setId("end-1");
        endNode.setName("End");
        endNode.setType("end");
        nodes.put("end-1", endNode);

        workflowDefinition.setNodes(nodes);

        // 创建边定义
        Map<String, List<EdgeDefinition>> edges = new HashMap<>();
        EdgeDefinition edge = new EdgeDefinition();
        edge.setId("edge-1");
        edge.setSourceNodeId("start-1");
        edge.setTargetNodeId("end-1");
        edges.put("start-1", List.of(edge));

        workflowDefinition.setEdges(edges);

        // 输入数据
        inputs = new HashMap<>();
        inputs.put("input1", "test value");
    }

    @Test
    void executeWorkflow_SimpleFlow_Success() throws Exception {
        // Given
        when(nodeFactory.createNode(any(NodeDefinition.class)))
            .thenAnswer(invocation -> {
                NodeDefinition def = invocation.getArgument(0);
                if ("start".equals(def.getType())) {
                    StartNode node = new StartNode();
                    node.setDefinition(def);
                    return node;
                } else if ("end".equals(def.getType())) {
                    EndNode node = new EndNode();
                    node.setDefinition(def);
                    return node;
                }
                return null;
            });

        // When
        CompletableFuture<WorkflowResult> future = workflowEngine.executeWorkflow(workflowDefinition, inputs);
        WorkflowResult result = future.get();

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(WorkflowExecutionStatus.COMPLETED, result.getStatus());
        assertNotNull(result.getOutputs());
        
        verify(nodeFactory, atLeast(1)).createNode(any(NodeDefinition.class));
    }

    @Test
    void executeWorkflow_NoStartNode_Failure() throws Exception {
        // Given
        workflowDefinition.getNodes().clear(); // 移除所有节点

        // When
        CompletableFuture<WorkflowResult> future = workflowEngine.executeWorkflow(workflowDefinition, inputs);
        WorkflowResult result = future.get();

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(WorkflowExecutionStatus.FAILED, result.getStatus());
        assertNotNull(result.getError());
        assertTrue(result.getError().contains("未找到开始节点"));
    }

    @Test
    void stopWorkflow_Success() {
        // Given
        String executionId = "execution-123";

        // When
        workflowEngine.stopWorkflow(executionId);

        // Then
        WorkflowExecutionStatus status = workflowEngine.getExecutionStatus(executionId);
        assertEquals(WorkflowExecutionStatus.NOT_FOUND, status);
    }

    @Test
    void getExecutionStatus_NotFound() {
        // Given
        String executionId = "non-existent";

        // When
        WorkflowExecutionStatus status = workflowEngine.getExecutionStatus(executionId);

        // Then
        assertEquals(WorkflowExecutionStatus.NOT_FOUND, status);
    }
}
