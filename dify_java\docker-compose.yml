version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: dify-postgres
    environment:
      POSTGRES_DB: dify
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: difyai123456
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - dify-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d dify"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: dify-redis
    command: redis-server --requirepass difyai123456
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - dify-network
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Weaviate向量数据库
  weaviate:
    image: semitechnologies/weaviate:1.22.4
    container_name: dify-weaviate
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate
    ports:
      - "8080:8080"
    networks:
      - dify-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/v1/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Console API服务
  console-api:
    build:
      context: .
      dockerfile: dify-console-api/Dockerfile
    container_name: dify-console-api
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: dify
      DB_USER: postgres
      DB_PASSWORD: difyai123456
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: difyai123456
      REDIS_DB: 0
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY:-}
    ports:
      - "5001:5001"
    networks:
      - dify-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/console/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Service API服务
  service-api:
    build:
      context: .
      dockerfile: dify-service-api/Dockerfile
    container_name: dify-service-api
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: dify
      DB_USER: postgres
      DB_PASSWORD: difyai123456
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: difyai123456
      REDIS_DB: 0
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY:-}
      WEAVIATE_HOST: weaviate
      WEAVIATE_PORT: 8080
    ports:
      - "5002:5002"
    networks:
      - dify-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      weaviate:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: dify-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
    ports:
      - "80:80"
      - "443:443"
    networks:
      - dify-network
    depends_on:
      - console-api
      - service-api
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  weaviate_data:
    driver: local

networks:
  dify-network:
    driver: bridge
