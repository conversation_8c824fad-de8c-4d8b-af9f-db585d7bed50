# Dify Java API 文档

## 概述

Dify Java 提供两套API服务：

1. **Console API** (端口5001): 管理控制台API，用于应用管理、数据集管理等
2. **Service API** (端口5002): 外部服务API，用于聊天对话、工作流执行等

## Console API

### 认证

所有Console API请求都需要JWT认证：

```http
Authorization: Bearer <jwt_token>
```

### 应用管理

#### 创建应用

```http
POST /console/api/apps
Content-Type: application/json

{
  "name": "My App",
  "description": "Application description",
  "mode": "CHAT",
  "enable_site": true,
  "enable_api": true
}
```

#### 获取应用列表

```http
GET /console/api/apps?page=1&size=20
```

#### 获取应用详情

```http
GET /console/api/apps/{app_id}
```

#### 更新应用

```http
PUT /console/api/apps/{app_id}
Content-Type: application/json

{
  "name": "Updated App Name",
  "description": "Updated description"
}
```

#### 删除应用

```http
DELETE /console/api/apps/{app_id}
```

#### 发布应用

```http
POST /console/api/apps/{app_id}/publish
```

### 数据集管理

#### 创建数据集

```http
POST /console/api/datasets
Content-Type: application/json

{
  "name": "My Dataset",
  "description": "Dataset description"
}
```

#### 上传文档

```http
POST /console/api/datasets/{dataset_id}/documents
Content-Type: multipart/form-data

file: <file_content>
```

#### 获取数据集列表

```http
GET /console/api/datasets?page=1&size=20
```

### 工作流管理

#### 创建工作流

```http
POST /console/api/workflows
Content-Type: application/json

{
  "app_id": "app-123",
  "type": "WORKFLOW",
  "graph": {
    "nodes": [...],
    "edges": [...]
  }
}
```

#### 执行工作流

```http
POST /console/api/workflows/{workflow_id}/run
Content-Type: application/json

{
  "inputs": {
    "input1": "value1"
  }
}
```

## Service API

### 认证

Service API使用API Key认证：

```http
Authorization: Bearer <api_key>
```

### 聊天对话

#### 发送消息

```http
POST /v1/chat-messages
Content-Type: application/json

{
  "inputs": {},
  "query": "Hello, how are you?",
  "response_mode": "blocking",
  "conversation_id": "",
  "user": "user-123"
}
```

#### 流式聊天

```http
POST /v1/chat-messages
Content-Type: application/json

{
  "inputs": {},
  "query": "Hello, how are you?",
  "response_mode": "streaming",
  "conversation_id": "",
  "user": "user-123"
}
```

响应格式（SSE）：
```
data: {"event": "message", "message_id": "msg-123", "conversation_id": "conv-123", "answer": "Hello! I'm doing well."}

data: {"event": "message_end", "message_id": "msg-123", "conversation_id": "conv-123"}
```

#### 获取对话历史

```http
GET /v1/conversations/{conversation_id}/messages?limit=20
```

### 工作流执行

#### 执行工作流

```http
POST /v1/workflows/run
Content-Type: application/json

{
  "inputs": {
    "input1": "value1",
    "input2": "value2"
  },
  "response_mode": "blocking",
  "user": "user-123"
}
```

#### 流式工作流执行

```http
POST /v1/workflows/run
Content-Type: application/json

{
  "inputs": {
    "input1": "value1"
  },
  "response_mode": "streaming",
  "user": "user-123"
}
```

### 知识检索

#### 检索文档

```http
POST /v1/datasets/{dataset_id}/retrieve
Content-Type: application/json

{
  "query": "search query",
  "top_k": 5,
  "score_threshold": 0.5
}
```

### 文件上传

#### 上传文件

```http
POST /v1/files/upload
Content-Type: multipart/form-data

file: <file_content>
user: user-123
```

## 错误处理

所有API都使用统一的错误响应格式：

```json
{
  "code": "PARAM_ERROR",
  "message": "参数错误",
  "details": "具体错误信息"
}
```

### 常见错误码

- `SUCCESS`: 成功
- `PARAM_ERROR`: 参数错误
- `UNAUTHORIZED`: 未授权
- `FORBIDDEN`: 禁止访问
- `NOT_FOUND`: 资源不存在
- `INTERNAL_ERROR`: 内部错误
- `MODEL_REQUEST_ERROR`: 模型请求错误
- `WORKFLOW_EXECUTION_ERROR`: 工作流执行错误
- `RETRIEVAL_ERROR`: 检索错误

## 限流

API请求受到限流保护：

- Console API: 每分钟20次请求
- Service API: 每分钟60次请求，每小时1000次请求

超出限制时返回429状态码：

```json
{
  "code": "RATE_LIMIT_EXCEEDED",
  "message": "请求频率超限",
  "retry_after": 60
}
```

## SDK示例

### Java SDK

```java
// 初始化客户端
DifyClient client = new DifyClient("your-api-key");

// 发送聊天消息
ChatRequest request = ChatRequest.builder()
    .query("Hello")
    .user("user-123")
    .build();

ChatResponse response = client.chat(request);
System.out.println(response.getAnswer());
```

### Python SDK

```python
from dify_client import DifyClient

# 初始化客户端
client = DifyClient(api_key="your-api-key")

# 发送聊天消息
response = client.chat(
    query="Hello",
    user="user-123"
)

print(response.answer)
```

### JavaScript SDK

```javascript
import { DifyClient } from 'dify-client';

// 初始化客户端
const client = new DifyClient('your-api-key');

// 发送聊天消息
const response = await client.chat({
  query: 'Hello',
  user: 'user-123'
});

console.log(response.answer);
```

## Webhook

### 配置Webhook

在应用设置中配置Webhook URL，系统会在特定事件发生时发送POST请求。

### 事件类型

- `message.created`: 消息创建
- `message.completed`: 消息完成
- `workflow.started`: 工作流开始
- `workflow.completed`: 工作流完成
- `workflow.failed`: 工作流失败

### Webhook格式

```json
{
  "event": "message.completed",
  "timestamp": "2024-06-23T10:00:00Z",
  "data": {
    "message_id": "msg-123",
    "conversation_id": "conv-123",
    "answer": "Hello! How can I help you?",
    "usage": {
      "prompt_tokens": 10,
      "completion_tokens": 20,
      "total_tokens": 30
    }
  }
}
```

## 版本控制

API使用版本控制，当前版本为v1。版本信息包含在URL路径中：

- `/v1/chat-messages`
- `/v1/workflows/run`

新版本发布时会保持向后兼容，废弃的API会提前通知。
