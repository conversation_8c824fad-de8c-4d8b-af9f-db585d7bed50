# Dify Java 部署指南

## 部署方式

Dify Java 支持多种部署方式：

1. **Docker Compose** (推荐)
2. **Kubernetes**
3. **传统部署**

## Docker Compose 部署

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少4GB内存
- 至少10GB磁盘空间

### 快速部署

1. **克隆项目**
```bash
git clone https://github.com/your-org/dify-java.git
cd dify-java
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，配置必要的API密钥
vim .env
```

3. **启动服务**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh start
```

4. **验证部署**
```bash
./scripts/deploy.sh health
```

### 服务访问

- Console API: http://localhost:5001/console
- Service API: http://localhost:5002
- Swagger文档: 
  - Console: http://localhost:5001/console/swagger-ui.html
  - Service: http://localhost:5002/swagger-ui.html

### 常用命令

```bash
# 查看服务状态
./scripts/deploy.sh status

# 查看日志
./scripts/deploy.sh logs

# 重启服务
./scripts/deploy.sh restart

# 停止服务
./scripts/deploy.sh stop

# 备份数据
./scripts/deploy.sh backup

# 清理资源
./scripts/deploy.sh cleanup
```

## Kubernetes 部署

### 前置要求

- Kubernetes 1.20+
- kubectl 配置正确
- Helm 3.0+ (可选)

### 使用 Helm 部署

1. **添加 Helm 仓库**
```bash
helm repo add dify https://charts.dify.ai
helm repo update
```

2. **创建命名空间**
```bash
kubectl create namespace dify
```

3. **配置 values.yaml**
```yaml
# values.yaml
global:
  storageClass: "standard"
  
postgresql:
  enabled: true
  auth:
    postgresPassword: "difyai123456"
    database: "dify"

redis:
  enabled: true
  auth:
    password: "difyai123456"

weaviate:
  enabled: true

consoleApi:
  image:
    repository: dify/console-api
    tag: "latest"
  env:
    OPENAI_API_KEY: "your-api-key"

serviceApi:
  image:
    repository: dify/service-api
    tag: "latest"
  env:
    OPENAI_API_KEY: "your-api-key"
```

4. **部署应用**
```bash
helm install dify dify/dify-java -n dify -f values.yaml
```

### 使用 kubectl 部署

1. **创建配置文件**
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dify

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dify-config
  namespace: dify
data:
  POSTGRES_HOST: "postgres"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "dify"
  REDIS_HOST: "redis"
  REDIS_PORT: "6379"

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: dify-secret
  namespace: dify
type: Opaque
data:
  POSTGRES_PASSWORD: ZGlmeWFpMTIzNDU2  # base64 encoded
  REDIS_PASSWORD: ZGlmeWFpMTIzNDU2     # base64 encoded
  OPENAI_API_KEY: eW91ci1hcGkta2V5      # base64 encoded
```

2. **部署数据库**
```yaml
# postgres.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: POSTGRES_DB
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dify-secret
              key: POSTGRES_PASSWORD
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: dify
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

3. **部署应用**
```bash
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f postgres.yaml
kubectl apply -f redis.yaml
kubectl apply -f console-api.yaml
kubectl apply -f service-api.yaml
```

## 传统部署

### 前置要求

- Java 17+
- Maven 3.6+
- PostgreSQL 15+
- Redis 7+
- Nginx (可选)

### 部署步骤

1. **准备环境**
```bash
# 安装Java 17
sudo apt update
sudo apt install openjdk-17-jdk

# 安装Maven
sudo apt install maven

# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib

# 安装Redis
sudo apt install redis-server
```

2. **配置数据库**
```bash
# 创建数据库
sudo -u postgres createdb dify
sudo -u postgres psql -c "CREATE USER dify WITH PASSWORD 'difyai123456';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE dify TO dify;"

# 导入初始化脚本
sudo -u postgres psql -d dify -f scripts/init.sql
```

3. **构建应用**
```bash
# 克隆代码
git clone https://github.com/your-org/dify-java.git
cd dify-java

# 构建项目
chmod +x scripts/build.sh
./scripts/build.sh
```

4. **配置应用**
```bash
# 复制配置文件
cp dify-console-api/src/main/resources/application.yml /etc/dify/console-api.yml
cp dify-service-api/src/main/resources/application.yml /etc/dify/service-api.yml

# 修改配置
vim /etc/dify/console-api.yml
vim /etc/dify/service-api.yml
```

5. **启动服务**
```bash
# 启动Console API
java -jar dify-console-api/target/dify-console-api-1.0.0-SNAPSHOT.jar \
  --spring.config.location=/etc/dify/console-api.yml &

# 启动Service API
java -jar dify-service-api/target/dify-service-api-1.0.0-SNAPSHOT.jar \
  --spring.config.location=/etc/dify/service-api.yml &
```

6. **配置Nginx (可选)**
```nginx
# /etc/nginx/sites-available/dify
server {
    listen 80;
    server_name your-domain.com;

    location /console/ {
        proxy_pass http://localhost:5001/console/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /v1/ {
        proxy_pass http://localhost:5002/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 生产环境配置

### 性能优化

1. **JVM参数**
```bash
-Xmx4g -Xms2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/log/dify/
```

2. **数据库优化**
```sql
-- PostgreSQL配置优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();
```

3. **Redis优化**
```conf
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 监控配置

1. **应用监控**
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

2. **日志配置**
```yaml
logging:
  level:
    root: WARN
    ai.dify: INFO
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: /var/log/dify/application.log
    max-size: 100MB
    max-history: 30
```

### 安全配置

1. **HTTPS配置**
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    
    # 其他配置...
}
```

2. **防火墙配置**
```bash
# 只开放必要端口
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 故障排除

### 常见问题

1. **服务启动失败**
```bash
# 检查日志
docker-compose logs console-api
docker-compose logs service-api

# 检查端口占用
netstat -tlnp | grep :5001
netstat -tlnp | grep :5002
```

2. **数据库连接失败**
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 测试连接
psql -h localhost -p 5432 -U postgres -d dify
```

3. **内存不足**
```bash
# 检查内存使用
free -h
docker stats

# 调整JVM参数
export JAVA_OPTS="-Xmx2g -Xms1g"
```

### 性能调优

1. **数据库性能**
```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 分析查询计划
EXPLAIN ANALYZE SELECT * FROM apps WHERE tenant_id = 'xxx';
```

2. **应用性能**
```bash
# 查看JVM状态
jstat -gc <pid>

# 查看线程状态
jstack <pid>

# 查看内存使用
jmap -histo <pid>
```

## 备份和恢复

### 数据备份

```bash
# 数据库备份
pg_dump -h localhost -U postgres dify > backup.sql

# Redis备份
redis-cli --rdb dump.rdb

# 文件备份
tar -czf files-backup.tar.gz /path/to/files
```

### 数据恢复

```bash
# 数据库恢复
psql -h localhost -U postgres dify < backup.sql

# Redis恢复
redis-cli --pipe < dump.rdb

# 文件恢复
tar -xzf files-backup.tar.gz -C /
```
