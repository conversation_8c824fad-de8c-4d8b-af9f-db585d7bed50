# Dify Java 快速开始指南

## 前置要求

### 系统要求
- **Java**: JDK 17 或更高版本
- **Maven**: 3.8.0 或更高版本
- **Docker**: 20.10 或更高版本
- **Docker Compose**: 2.0 或更高版本

### 硬件要求
- **内存**: 最少 4GB，推荐 8GB
- **存储**: 最少 10GB 可用空间
- **CPU**: 2 核心或更多

## 快速部署

### 方式一：Docker Compose（推荐）

1. **克隆项目**
```bash
git clone https://github.com/your-org/dify-java.git
cd dify-java
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
vim .env
```

3. **一键启动**
```bash
# 启动所有服务
./scripts/deploy.sh start

# 检查服务状态
./scripts/deploy.sh health

# 查看日志
./scripts/deploy.sh logs
```

4. **访问服务**
- **管理控制台**: http://localhost:5001
- **API 服务**: http://localhost:5002
- **API 文档**: http://localhost:5002/swagger-ui.html

### 方式二：本地开发

1. **启动依赖服务**
```bash
# 启动 PostgreSQL 和 Redis
docker-compose up -d postgres redis weaviate
```

2. **构建项目**
```bash
mvn clean install -DskipTests
```

3. **启动应用**
```bash
# 启动 Console API
cd dify-console-api
mvn spring-boot:run

# 启动 Service API（新终端）
cd dify-service-api
mvn spring-boot:run
```

## 基本配置

### 环境变量配置

编辑 `.env` 文件：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=dify
DB_USERNAME=dify
DB_PASSWORD=dify123

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 向量数据库配置
WEAVIATE_URL=http://localhost:8080

# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1

# 应用配置
APP_SECRET_KEY=your_secret_key
APP_WEB_URL=http://localhost:3000
APP_API_URL=http://localhost:5002
APP_CONSOLE_URL=http://localhost:5001
```

### 数据库初始化

```bash
# 自动创建数据库表（首次启动时）
# 应用会自动执行数据库迁移
```

## 第一个应用

### 1. 创建应用

```bash
curl -X POST http://localhost:5001/console/api/apps \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_console_token" \
  -d '{
    "name": "我的第一个应用",
    "mode": "CHAT",
    "description": "这是我的第一个 AI 应用"
  }'
```

### 2. 配置模型

```bash
curl -X PUT http://localhost:5001/console/api/apps/{app_id}/model-config \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_console_token" \
  -d '{
    "provider": "openai",
    "model": "gpt-3.5-turbo",
    "parameters": {
      "temperature": 0.7,
      "max_tokens": 1000
    }
  }'
```

### 3. 发布应用

```bash
curl -X POST http://localhost:5001/console/api/apps/{app_id}/publish \
  -H "Authorization: Bearer your_console_token"
```

### 4. 测试对话

```bash
curl -X POST http://localhost:5002/v1/chat-messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_app_api_key" \
  -d '{
    "query": "你好，请介绍一下自己",
    "user": "user123",
    "response_mode": "blocking"
  }'
```

## 工作流应用

### 1. 创建工作流应用

```bash
curl -X POST http://localhost:5001/console/api/apps \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_console_token" \
  -d '{
    "name": "工作流应用",
    "mode": "WORKFLOW",
    "description": "基于工作流的应用"
  }'
```

### 2. 设计工作流

```json
{
  "nodes": [
    {
      "id": "start",
      "type": "start",
      "position": {"x": 100, "y": 100},
      "config": {
        "inputs": ["query"]
      }
    },
    {
      "id": "llm",
      "type": "llm",
      "position": {"x": 300, "y": 100},
      "config": {
        "provider": "openai",
        "model": "gpt-3.5-turbo",
        "prompt": "请回答用户的问题：{{query}}"
      }
    },
    {
      "id": "end",
      "type": "end",
      "position": {"x": 500, "y": 100},
      "config": {
        "outputs": ["answer"]
      }
    }
  ],
  "edges": [
    {"source": "start", "target": "llm"},
    {"source": "llm", "target": "end"}
  ]
}
```

### 3. 执行工作流

```bash
curl -X POST http://localhost:5002/v1/workflows/run \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_app_api_key" \
  -d '{
    "inputs": {
      "query": "什么是人工智能？"
    },
    "user": "user123"
  }'
```

## 文件上传

### 上传文档

```bash
curl -X POST http://localhost:5002/v1/files/upload \
  -H "Authorization: Bearer your_app_api_key" \
  -F "file=@document.pdf" \
  -F "user=user123"
```

### 创建知识库

```bash
curl -X POST http://localhost:5001/console/api/datasets \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_console_token" \
  -d '{
    "name": "我的知识库",
    "description": "包含重要文档的知识库"
  }'
```

## 监控和日志

### 查看应用状态

```bash
# 健康检查
curl http://localhost:5001/actuator/health
curl http://localhost:5002/actuator/health

# 指标信息
curl http://localhost:5001/actuator/metrics
curl http://localhost:5002/actuator/metrics
```

### 查看日志

```bash
# 查看应用日志
docker-compose logs -f console-api
docker-compose logs -f service-api

# 查看数据库日志
docker-compose logs -f postgres
```

## 常见问题

### Q: 启动失败怎么办？

A: 检查以下几点：
1. 确保 Docker 和 Docker Compose 正常运行
2. 检查端口是否被占用（5001, 5002, 5432, 6379, 8080）
3. 查看日志文件排查具体错误
4. 确保环境变量配置正确

### Q: API 调用返回 401 错误？

A: 检查以下几点：
1. 确保 API 密钥正确
2. 检查应用是否已发布
3. 验证 Authorization 头格式是否正确

### Q: 模型调用失败？

A: 检查以下几点：
1. 确保 OpenAI API 密钥有效
2. 检查网络连接是否正常
3. 验证模型名称是否正确
4. 查看应用日志获取详细错误信息

### Q: 如何停止服务？

A: 使用以下命令：
```bash
# 停止所有服务
./scripts/deploy.sh stop

# 或者使用 docker-compose
docker-compose down
```

## 下一步

1. **阅读详细文档**: 查看 `docs/` 目录下的详细文档
2. **探索 API**: 访问 Swagger UI 了解完整的 API
3. **自定义开发**: 参考开发指南进行功能扩展
4. **生产部署**: 查看生产环境部署指南

## 获取帮助

- **文档**: 查看 `docs/` 目录
- **示例**: 查看 `examples/` 目录
- **问题反馈**: 提交 GitHub Issue
- **社区讨论**: 加入开发者社区

祝您使用愉快！🎉
