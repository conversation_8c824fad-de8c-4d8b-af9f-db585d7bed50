<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>ai.dify</groupId>
    <artifactId>dify-java</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>Dify Java</name>
    <description>Java implementation of Dify AI application development platform</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot版本 -->
        <spring-boot.version>3.2.0</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        
        <!-- 数据库相关 -->
        <postgresql.version>42.7.1</postgresql.version>
        <redis.version>3.2.0</redis.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        
        <!-- 工具库 -->
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <jackson.version>2.16.0</jackson.version>
        <hutool.version>5.8.23</hutool.version>
        
        <!-- 向量数据库 -->
        <weaviate.version>4.4.0</weaviate.version>
        <qdrant.version>1.7.0</qdrant.version>
        
        <!-- HTTP客户端 -->
        <okhttp.version>4.12.0</okhttp.version>
        <retrofit.version>2.9.0</retrofit.version>
        
        <!-- 测试 -->
        <junit.version>5.10.1</junit.version>
        <testcontainers.version>1.19.3</testcontainers.version>
    </properties>

    <modules>
        <module>dify-common</module>
        <module>dify-domain</module>
        <module>dify-model-runtime</module>
        <module>dify-workflow-engine</module>
        <module>dify-rag-service</module>
        <module>dify-plugin-system</module>
        <module>dify-agent-system</module>
        <module>dify-multimodal</module>
        <module>dify-file-service</module>
        <module>dify-monitoring</module>
        <module>dify-task-queue</module>
        <module>dify-tools</module>
        <module>dify-cache</module>
        <module>dify-security</module>
        <module>dify-tenant</module>
        <module>dify-web</module>
        <module>dify-console-api</module>
        <module>dify-service-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 内部模块 -->
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-model-runtime</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-rag-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-workflow-engine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-plugin-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-agent-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-multimodal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-file-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-monitoring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-task-queue</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-tools</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>ai.dify</groupId>
                <artifactId>dify-tenant</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 数据库 -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- 工具库 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- HTTP客户端 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
            </dependency>

            <!-- 向量数据库 -->
            <dependency>
                <groupId>io.weaviate</groupId>
                <artifactId>client</artifactId>
                <version>${weaviate.version}</version>
            </dependency>

            <!-- 测试 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
