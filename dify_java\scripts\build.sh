#!/bin/bash

# Dify Java 构建脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查构建依赖..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请安装Java 17或更高版本"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请安装Maven 3.6或更高版本"
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_warning "Docker未安装，将跳过Docker镜像构建"
        SKIP_DOCKER=true
    fi
    
    log_success "依赖检查完成"
}

# 清理构建目录
clean_build() {
    log_info "清理构建目录..."
    mvn clean
    log_success "构建目录清理完成"
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    mvn compile -DskipTests
    log_success "项目编译完成"
}

# 运行测试
run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        log_warning "跳过测试"
        return
    fi
    
    log_info "运行测试..."
    mvn test
    log_success "测试完成"
}

# 打包项目
package_project() {
    log_info "打包项目..."
    mvn package -DskipTests
    log_success "项目打包完成"
}

# 构建Docker镜像
build_docker_images() {
    if [ "$SKIP_DOCKER" = true ]; then
        log_warning "跳过Docker镜像构建"
        return
    fi
    
    log_info "构建Docker镜像..."
    
    # 构建Console API镜像
    log_info "构建Console API镜像..."
    docker build -f dify-console-api/Dockerfile -t dify-console-api:latest .
    
    # 构建Service API镜像
    log_info "构建Service API镜像..."
    docker build -f dify-service-api/Dockerfile -t dify-service-api:latest .
    
    log_success "Docker镜像构建完成"
}

# 生成构建报告
generate_report() {
    log_info "生成构建报告..."
    
    REPORT_FILE="build-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
Dify Java 构建报告
==================

构建时间: $(date)
构建版本: $(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)

模块列表:
- dify-common: 公共工具模块
- dify-domain: 领域模型模块
- dify-model-runtime: 模型运行时模块
- dify-workflow-engine: 工作流引擎模块
- dify-rag-service: RAG服务模块
- dify-console-api: 控制台API服务
- dify-service-api: 外部API服务

构建状态: 成功
EOF
    
    if [ "$SKIP_DOCKER" != true ]; then
        echo "" >> "$REPORT_FILE"
        echo "Docker镜像:" >> "$REPORT_FILE"
        docker images | grep dify >> "$REPORT_FILE" || true
    fi
    
    log_success "构建报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    log_info "开始构建Dify Java项目..."
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-docker)
                SKIP_DOCKER=true
                shift
                ;;
            --clean)
                CLEAN_BUILD=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-tests    跳过测试"
                echo "  --skip-docker   跳过Docker镜像构建"
                echo "  --clean         清理构建目录"
                echo "  -h, --help      显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行构建步骤
    check_dependencies
    
    if [ "$CLEAN_BUILD" = true ]; then
        clean_build
    fi
    
    compile_project
    run_tests
    package_project
    build_docker_images
    generate_report
    
    log_success "Dify Java项目构建完成！"
}

# 错误处理
trap 'log_error "构建失败！"; exit 1' ERR

# 执行主函数
main "$@"
