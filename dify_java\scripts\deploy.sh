#!/bin/bash

# Dify Java 部署脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="backups"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查环境文件
check_env_file() {
    log_info "检查环境配置..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "环境文件不存在，创建默认配置..."
        cat > "$ENV_FILE" << EOF
# Dify Java 环境配置

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 数据库配置
POSTGRES_PASSWORD=difyai123456
REDIS_PASSWORD=difyai123456

# 其他配置
ENVIRONMENT=production
LOG_LEVEL=INFO
EOF
        log_warning "请编辑 $ENV_FILE 文件，配置必要的API密钥"
    fi
    
    log_success "环境配置检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p logs
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/weaviate
    
    log_success "目录创建完成"
}

# 备份数据
backup_data() {
    if [ "$SKIP_BACKUP" = true ]; then
        log_warning "跳过数据备份"
        return
    fi
    
    log_info "备份数据..."
    
    BACKUP_FILE="$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    if [ -d "data" ]; then
        tar -czf "$BACKUP_FILE" data/ || log_warning "数据备份失败"
        log_success "数据已备份到: $BACKUP_FILE"
    else
        log_warning "没有找到数据目录，跳过备份"
    fi
}

# 拉取镜像
pull_images() {
    log_info "拉取Docker镜像..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose pull
    else
        docker compose pull
    fi
    
    log_success "镜像拉取完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down
    else
        docker compose down
    fi
    
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    stop_services
    start_services
    
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
}

# 查看日志
show_logs() {
    local service=$1
    
    if [ -z "$service" ]; then
        log_info "显示所有服务日志:"
        if command -v docker-compose &> /dev/null; then
            docker-compose logs -f
        else
            docker compose logs -f
        fi
    else
        log_info "显示 $service 服务日志:"
        if command -v docker-compose &> /dev/null; then
            docker-compose logs -f "$service"
        else
            docker compose logs -f "$service"
        fi
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康检查尝试 $attempt/$max_attempts"
        
        # 检查Console API
        if curl -f -s http://localhost:5001/console/actuator/health > /dev/null; then
            log_success "Console API 健康检查通过"
        else
            log_warning "Console API 健康检查失败"
        fi
        
        # 检查Service API
        if curl -f -s http://localhost:5002/actuator/health > /dev/null; then
            log_success "Service API 健康检查通过"
            break
        else
            log_warning "Service API 健康检查失败"
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "健康检查失败，请检查服务状态"
            return 1
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log_success "健康检查完成"
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    log_success "资源清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
Dify Java 部署脚本

用法: $0 [命令] [选项]

命令:
  start           启动所有服务
  stop            停止所有服务
  restart         重启所有服务
  status          显示服务状态
  logs [service]  显示日志
  health          执行健康检查
  backup          备份数据
  cleanup         清理Docker资源
  help            显示帮助信息

选项:
  --skip-backup   跳过数据备份
  --no-pull       不拉取最新镜像

示例:
  $0 start                    # 启动所有服务
  $0 logs console-api         # 显示Console API日志
  $0 restart --skip-backup    # 重启服务但跳过备份
EOF
}

# 主函数
main() {
    local command=$1
    shift || true
    
    # 解析选项
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --no-pull)
                NO_PULL=true
                shift
                ;;
            *)
                break
                ;;
        esac
    done
    
    case $command in
        start)
            check_dependencies
            check_env_file
            create_directories
            if [ "$NO_PULL" != true ]; then
                pull_images
            fi
            start_services
            health_check
            ;;
        stop)
            stop_services
            ;;
        restart)
            backup_data
            restart_services
            health_check
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$1"
            ;;
        health)
            health_check
            ;;
        backup)
            backup_data
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            log_error "请指定命令，使用 --help 查看帮助"
            exit 1
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 错误处理
trap 'log_error "部署失败！"; exit 1' ERR

# 执行主函数
main "$@"
