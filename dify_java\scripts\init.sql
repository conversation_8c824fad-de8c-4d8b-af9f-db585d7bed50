-- Dify Java 数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS dify;
CREATE DATABASE IF NOT EXISTS dify_dev;
CREATE DATABASE IF NOT EXISTS dify_test;

-- 使用dify数据库
\c dify;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建账户表
CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255),
    email VARCHAR(255) UNIQUE NOT NULL,
    avatar VARCHAR(500),
    password_hash VARCHAR(255),
    password_salt VARCHAR(255),
    interface_language VARCHAR(10) DEFAULT 'en-US',
    interface_theme VARCHAR(20) DEFAULT 'light',
    timezone VARCHAR(50) DEFAULT 'UTC',
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(45),
    status VARCHAR(20) DEFAULT 'active',
    initialization_vector VARCHAR(255),
    initialized BOOLEAN DEFAULT FALSE,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- 创建应用表
CREATE TABLE IF NOT EXISTS apps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(500),
    icon_background VARCHAR(20),
    mode VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    enable_site BOOLEAN DEFAULT FALSE,
    enable_api BOOLEAN DEFAULT FALSE,
    api_rpm INTEGER,
    api_rpm_enabled BOOLEAN DEFAULT FALSE,
    api_tpm INTEGER,
    api_tpm_enabled BOOLEAN DEFAULT FALSE,
    app_model_config JSONB,
    workflow_id UUID,
    use_icon_as_answer_icon BOOLEAN DEFAULT FALSE,
    max_conversation_length INTEGER,
    opening_statement TEXT,
    suggested_questions JSONB,
    suggested_questions_after_answer_enabled BOOLEAN DEFAULT FALSE,
    speech_to_text_enabled BOOLEAN DEFAULT FALSE,
    text_to_speech_enabled BOOLEAN DEFAULT FALSE,
    more_like_this_enabled BOOLEAN DEFAULT FALSE,
    user_input_form JSONB,
    is_template BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    is_universal BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- 创建数据集表
CREATE TABLE IF NOT EXISTS datasets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'available',
    data_source_type VARCHAR(50),
    index_struct VARCHAR(50),
    vector_store VARCHAR(50),
    vector_store_config JSONB,
    retrieval_model_config JSONB,
    document_count INTEGER DEFAULT 0,
    character_count BIGINT DEFAULT 0,
    segment_count INTEGER DEFAULT 0,
    embedding_model VARCHAR(100),
    embedding_model_provider VARCHAR(50),
    permission VARCHAR(20) DEFAULT 'only_me',
    provider VARCHAR(50),
    dataset_process_rule JSONB,
    tags JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- 创建工作流表
CREATE TABLE IF NOT EXISTS workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL,
    app_id UUID NOT NULL,
    type VARCHAR(20) NOT NULL,
    version VARCHAR(20),
    graph JSONB,
    features JSONB,
    environment_variables JSONB,
    conversation_variables JSONB,
    is_published BOOLEAN DEFAULT FALSE,
    is_draft BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- 创建对话表
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    app_id UUID NOT NULL,
    app_model_config_id UUID,
    model_provider VARCHAR(100),
    model_id VARCHAR(100),
    override_model_configs JSONB,
    mode VARCHAR(20),
    name VARCHAR(255),
    summary TEXT,
    inputs JSONB,
    introduction TEXT,
    system_query TEXT,
    system_query_tokens INTEGER,
    status VARCHAR(20) DEFAULT 'normal',
    from_source VARCHAR(50),
    from_account_id UUID,
    from_end_user_id VARCHAR(255),
    read_at TIMESTAMP,
    read_account_id UUID,
    is_pinned BOOLEAN DEFAULT FALSE,
    invoke_from VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- 创建消息表
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    app_id UUID NOT NULL,
    model_config_id UUID,
    conversation_id UUID NOT NULL,
    inputs JSONB,
    query TEXT,
    message JSONB,
    message_tokens INTEGER,
    message_unit_price DECIMAL(10, 6),
    message_price_unit VARCHAR(10),
    answer TEXT,
    answer_tokens INTEGER,
    answer_unit_price DECIMAL(10, 6),
    answer_price_unit VARCHAR(10),
    provider_response_latency DECIMAL(10, 4),
    total_tokens INTEGER,
    total_price DECIMAL(10, 6),
    currency VARCHAR(10),
    from_source VARCHAR(50),
    from_account_id UUID,
    from_end_user_id VARCHAR(255),
    status VARCHAR(20) DEFAULT 'normal',
    error TEXT,
    message_metadata JSONB,
    invoke_from VARCHAR(50),
    workflow_run_id UUID,
    parent_message_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_accounts_email ON accounts(email);
CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status);

CREATE INDEX IF NOT EXISTS idx_apps_tenant_id ON apps(tenant_id);
CREATE INDEX IF NOT EXISTS idx_apps_status ON apps(status);
CREATE INDEX IF NOT EXISTS idx_apps_mode ON apps(mode);
CREATE INDEX IF NOT EXISTS idx_apps_name ON apps(name);

CREATE INDEX IF NOT EXISTS idx_datasets_tenant_id ON datasets(tenant_id);
CREATE INDEX IF NOT EXISTS idx_datasets_status ON datasets(status);
CREATE INDEX IF NOT EXISTS idx_datasets_name ON datasets(name);

CREATE INDEX IF NOT EXISTS idx_workflows_app_id ON workflows(app_id);
CREATE INDEX IF NOT EXISTS idx_workflows_tenant_id ON workflows(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflows_type ON workflows(type);

CREATE INDEX IF NOT EXISTS idx_conversations_app_id ON conversations(app_id);
CREATE INDEX IF NOT EXISTS idx_conversations_from_end_user_id ON conversations(from_end_user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at);

CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_app_id ON messages(app_id);
CREATE INDEX IF NOT EXISTS idx_messages_from_end_user_id ON messages(from_end_user_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_workflow_run_id ON messages(workflow_run_id);

-- 创建全文搜索索引
CREATE INDEX IF NOT EXISTS idx_apps_name_gin ON apps USING gin(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_datasets_name_gin ON datasets USING gin(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_conversations_name_gin ON conversations USING gin(name gin_trgm_ops);

-- 插入初始数据
INSERT INTO accounts (id, name, email, password_hash, status, initialized) 
VALUES (
    uuid_generate_v4(),
    'Admin',
    '<EMAIL>',
    '$2a$10$example_hash_here',
    'active',
    TRUE
) ON CONFLICT (email) DO NOTHING;
